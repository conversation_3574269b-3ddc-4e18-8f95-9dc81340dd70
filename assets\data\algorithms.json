{"3x3x3 Cube": {"name": "3x3x3 Cube", "category": "NxNxN Cube", "event_type": "WCA", "methods": {"CFOP": {"name": "CFOP", "description": "Cross, F2L, OLL, PLL - The most popular speedcubing method", "steps": {"OLL": {"name": "Orientation of Last Layer", "description": "Algorithms for orienting all pieces on the last layer", "cases": [{"id": "oll_1", "name": "Dot Case 1", "case_name": "1", "category": "<PERSON>", "algorithm": "(R U2 R') (R' F R F') U2 (R' F R F')", "alternative_algorithms": ["R' U' F R' F' R2 U R f' U' f"], "move_count": 12, "difficulty": 2, "description": "No edges oriented - dot case", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_2", "name": "Dot Case 2", "case_name": "2", "category": "<PERSON>", "algorithm": "F (R U R' U') F' f (R U R' U') f'", "alternative_algorithms": ["y r U r' U2 R U2 R' U2 r U' r'"], "move_count": 12, "difficulty": 2, "description": "No edges oriented - dot case", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_3", "name": "Dot Case 3", "case_name": "3", "category": "<PERSON>", "algorithm": "y' f (R U R' U') f' (U') F (R U R' U') F", "alternative_algorithms": ["r' R2 U R' U r U2 r' U M'"], "move_count": 14, "difficulty": 3, "description": "No edges oriented - dot case", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_4", "name": "Dot Case 4", "case_name": "4", "category": "<PERSON>", "algorithm": "y' f (R U R' U') f' (U) F (R U R' U') F", "alternative_algorithms": ["l L2 U' L U' l' U2 l U' M'"], "move_count": 14, "difficulty": 3, "description": "No edges oriented - dot case", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_5", "name": "Square Shape 5", "case_name": "5", "category": "Square Shape", "algorithm": "r' U2 (R U R' U) r", "alternative_algorithms": ["y2 l' U2 L U L' U l"], "move_count": 7, "difficulty": 1, "description": "Square shape pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_6", "name": "Square Shape 6", "case_name": "6", "category": "Square Shape", "algorithm": "r <PERSON> (R' U' R U') r'", "alternative_algorithms": ["y2 l U2 L' U' L U' l'"], "move_count": 7, "difficulty": 1, "description": "Square shape pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_7", "name": "Lightning Shape 7", "case_name": "7", "category": "Lightning Shape", "algorithm": "r (U R' U R) U2 r'", "alternative_algorithms": ["r U r' U R U' R' r U' r'"], "move_count": 7, "difficulty": 1, "description": "Lightning bolt shape", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_8", "name": "Lightning Shape 8", "case_name": "8", "category": "Lightning Shape", "algorithm": "y2 r' (U' R U' R') U2 r", "alternative_algorithms": ["l' U' L U' L' U2 l"], "move_count": 8, "difficulty": 2, "description": "Lightning bolt shape", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_9", "name": "Fish Shape 9", "case_name": "9", "category": "Fish Shape", "algorithm": "y (R U R' U') (R' F R) (R U R' U') F'", "alternative_algorithms": ["R U2 R' U' S' R U' R' S"], "move_count": 13, "difficulty": 2, "description": "Fish shape pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_10", "name": "Fish Shape 10", "case_name": "10", "category": "Fish Shape", "algorithm": "(R U R' U) (R' F R F') (R U2 R')", "alternative_algorithms": ["y F U F' R' F R U' R' F' R"], "move_count": 11, "difficulty": 2, "description": "Fish shape pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_11", "name": "Lightning Shape 11", "case_name": "11", "category": "Lightning Shape", "algorithm": "M (R U R' U R U2 R') U M'", "alternative_algorithms": ["y2 r U R' U R' F R F' R U2 r'"], "move_count": 10, "difficulty": 2, "description": "Lightning bolt shape", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_12", "name": "Lightning Shape 12", "case_name": "12", "category": "Lightning Shape", "algorithm": "y' M' (<PERSON>' <PERSON>' R U' R' U2 R) U' M", "alternative_algorithms": ["y l L2 U' L U' L' U2 L U' M'"], "move_count": 11, "difficulty": 2, "description": "Lightning bolt shape", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_13", "name": "Knight Move Shape 13", "case_name": "13", "category": "Knight Move Shape", "algorithm": "(r U' r') U' (r U r') (F' U F)", "alternative_algorithms": ["F U R U' R2 F' R U R U' R'"], "move_count": 10, "difficulty": 2, "description": "Knight move pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_14", "name": "Knight Move Shape 14", "case_name": "14", "category": "Knight Move Shape", "algorithm": "R' F (R U R') F' R (F U' F')", "alternative_algorithms": ["r U R' U' r' F R2 U R' U' F'"], "move_count": 10, "difficulty": 2, "description": "Knight move pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_15", "name": "Knight Move Shape 15", "case_name": "15", "category": "Knight Move Shape", "algorithm": "(r' U' r) (R' U' R U) (r' U r)", "alternative_algorithms": ["y2 l' U' l L' U' L U l' U l"], "move_count": 10, "difficulty": 2, "description": "Knight move pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_16", "name": "Knight Move Shape 16", "case_name": "16", "category": "Knight Move Shape", "algorithm": "(r U r') (R U R' U') (r U' r')", "alternative_algorithms": ["y2 R' F R U R' U' F' R U' R' U2 R"], "move_count": 10, "difficulty": 2, "description": "Knight move pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}]}, "PLL": {"name": "Permutation of Last Layer", "description": "Algorithms for permuting all pieces on the last layer", "cases": [{"id": "pll_aa", "name": "Aa Perm", "case_name": "Aa Perm", "category": "A Perm", "algorithm": "x (R' U R') D2 (R U' R') D2 R2 x'", "alternative_algorithms": [], "move_count": 11, "difficulty": 3, "description": "Clockwise corner 3-cycle", "source": "https://speedcubedb.com/a/3x3/PLL"}, {"id": "pll_ab", "name": "<PERSON><PERSON>", "case_name": "<PERSON><PERSON>", "category": "A Perm", "algorithm": "x R2 D2 (R U R') D2 (R U' R) x'", "alternative_algorithms": [], "move_count": 11, "difficulty": 3, "description": "Counter-clockwise corner 3-cycle", "source": "https://speedcubedb.com/a/3x3/PLL"}, {"id": "pll_e", "name": "E Perm", "case_name": "E Perm", "category": "E Perm", "algorithm": "y x' (R U' R' D) (R U R' D') (R U R' D) (R U' R' D') x", "alternative_algorithms": [], "move_count": 19, "difficulty": 4, "description": "Diagonal corner swap + diagonal edge swap", "source": "https://speedcubedb.com/a/3x3/PLL"}, {"id": "pll_f", "name": "F Perm", "case_name": "F Perm", "category": "F Perm", "algorithm": "y (R' U' F') (R U R' U') R' F R2 (U' R' U') (R U R' U) R", "alternative_algorithms": [], "move_count": 19, "difficulty": 3, "description": "Adjacent corner swap + adjacent edge swap", "source": "https://speedcubedb.com/a/3x3/PLL"}, {"id": "pll_ga", "name": "Ga Perm", "case_name": "Ga Perm", "category": "G Perm", "algorithm": "R2 (U R' U R' U' R U') R2 D (U' R' U R) D'", "alternative_algorithms": ["y R U R' F' R U R' U' R' F R U' R' F R2 U' R' U' R U R' F'"], "move_count": 15, "difficulty": 2, "description": "Adjacent corner swap + diagonal edge swap", "source": "https://speedcubedb.com/a/3x3/PLL"}, {"id": "pll_gb", "name": "Gb Perm", "case_name": "Gb Perm", "category": "G Perm", "algorithm": "(R' U' R U) D' R2 (U R' U R U' R U') R2 D", "alternative_algorithms": [], "move_count": 15, "difficulty": 2, "description": "Adjacent corner swap + diagonal edge swap", "source": "https://speedcubedb.com/a/3x3/PLL"}, {"id": "pll_gc", "name": "Gc Perm", "case_name": "Gc Perm", "category": "G Perm", "algorithm": "R2 (U' R U' R U R' U) R2 D' (U R U' R') D", "alternative_algorithms": ["y2 R2 F2 R U2 R U2 R' F R U R' U' R' F R2"], "move_count": 15, "difficulty": 2, "description": "Adjacent corner swap + diagonal edge swap", "source": "https://speedcubedb.com/a/3x3/PLL"}, {"id": "pll_gd", "name": "Gd Perm", "case_name": "Gd Perm", "category": "G Perm", "algorithm": "(R U R' U') D R2 (U' R U' R' U R' U) R2 D'", "alternative_algorithms": [], "move_count": 15, "difficulty": 2, "description": "Adjacent corner swap + diagonal edge swap", "source": "https://speedcubedb.com/a/3x3/PLL"}, {"id": "pll_h", "name": "H Perm", "case_name": "H Perm", "category": "H Perm", "algorithm": "(M2 U' M2) U2 (M2 U' M2)", "alternative_algorithms": [], "move_count": 7, "difficulty": 1, "description": "Opposite edge swap", "source": "https://speedcubedb.com/a/3x3/PLL"}, {"id": "pll_ja", "name": "<PERSON><PERSON>", "case_name": "<PERSON><PERSON>", "category": "J Perm", "algorithm": "y (R' U L') U2 (R U' R') U2 R L", "alternative_algorithms": ["y R' U L' U2 R U' R' U2 R L"], "move_count": 11, "difficulty": 2, "description": "Adjacent corner swap", "source": "https://speedcubedb.com/a/3x3/PLL"}, {"id": "pll_jb", "name": "<PERSON><PERSON>", "case_name": "<PERSON><PERSON>", "category": "J Perm", "algorithm": "(R U R' F') (R U R' U') R' F R2 U' R'", "alternative_algorithms": [], "move_count": 13, "difficulty": 2, "description": "Adjacent corner swap", "source": "https://speedcubedb.com/a/3x3/PLL"}]}, "F2L": {"name": "First Two Layers", "description": "Algorithms for solving the first two layers simultaneously", "cases": [{"id": "f2l_1", "name": "Case 1", "case_name": "1", "category": "Basic Insertion", "difficulty": 1, "description": "Basic corner-edge pair insertion", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "U R U' R'", "alternative_algorithms": [], "move_count": 4}, "Front-Left": {"algorithm": "F' r U r'", "alternative_algorithms": [], "move_count": 4}, "Back-Left": {"algorithm": "U L U' L'", "alternative_algorithms": [], "move_count": 4}, "Back-Right": {"algorithm": "U f R' f'", "alternative_algorithms": [], "move_count": 4}}}, {"id": "f2l_2", "name": "Case 2", "case_name": "2", "category": "Basic Insertion", "difficulty": 1, "description": "Corner and edge separated", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "F R' F' R", "alternative_algorithms": [], "move_count": 4}, "Front-Left": {"algorithm": "U' L' U L", "alternative_algorithms": [], "move_count": 4}, "Back-Left": {"algorithm": "l U L' U' M'", "alternative_algorithms": [], "move_count": 5}, "Back-Right": {"algorithm": "U' R' U R", "alternative_algorithms": [], "move_count": 4}}}, {"id": "f2l_3", "name": "Case 3", "case_name": "3", "category": "Corner Oriented", "difficulty": 1, "description": "Corner oriented, edge in top", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "F' U' F", "alternative_algorithms": [], "move_count": 3}, "Front-Left": {"algorithm": "L' U' L", "alternative_algorithms": [], "move_count": 3}, "Back-Left": {"algorithm": "y R' U' R", "alternative_algorithms": [], "move_count": 4}, "Back-Right": {"algorithm": "R' U' R", "alternative_algorithms": [], "move_count": 3}}}, {"id": "f2l_4", "name": "Case 4", "case_name": "4", "category": "Corner Oriented", "difficulty": 1, "description": "Corner oriented, edge in top", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "R U R'", "alternative_algorithms": [], "move_count": 3}, "Front-Left": {"algorithm": "F U F'", "alternative_algorithms": [], "move_count": 3}, "Back-Left": {"algorithm": "L U L", "alternative_algorithms": [], "move_count": 3}, "Back-Right": {"algorithm": "f R f'", "alternative_algorithms": [], "move_count": 3}}}, {"id": "f2l_5", "name": "Case 5", "case_name": "5", "category": "Edge Oriented Wrong", "difficulty": 1, "description": "Edge oriented wrong", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "U' R U R' U2 R U' R'", "alternative_algorithms": [], "move_count": 8}, "Front-Left": {"algorithm": "U R' F r U' r' F' R", "alternative_algorithms": [], "move_count": 8}, "Back-Left": {"algorithm": "U' L U L' U2 L U' L'", "alternative_algorithms": [], "move_count": 8}, "Back-Right": {"algorithm": "U' R' F R U R' U' F' R", "alternative_algorithms": [], "move_count": 9}}}]}}}}}}