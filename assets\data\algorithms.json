{"3x3x3 Cube": {"name": "3x3x3 Cube", "category": "NxNxN Cube", "event_type": "WCA", "methods": {"CFOP": {"name": "CFOP", "description": "Cross, F2L, OLL, PLL - The most popular speedcubing method", "steps": {"OLL": {"name": "Orientation of Last Layer", "description": "Algorithms for orienting all pieces on the last layer", "cases": [{"id": "oll_1", "name": "Dot Case 1", "case_name": "1", "category": "<PERSON>", "algorithm": "(R U2 R') (R' F R F') U2 (R' F R F')", "alternative_algorithms": ["R' U' F R' F' R2 U R f' U' f"], "move_count": 12, "difficulty": 2, "description": "No edges oriented - dot case", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_2", "name": "Dot Case 2", "case_name": "2", "category": "<PERSON>", "algorithm": "F (R U R' U') F' f (R U R' U') f'", "alternative_algorithms": ["y r U r' U2 R U2 R' U2 r U' r'"], "move_count": 12, "difficulty": 2, "description": "No edges oriented - dot case", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_3", "name": "Dot Case 3", "case_name": "3", "category": "<PERSON>", "algorithm": "y' f (R U R' U') f' (U') F (R U R' U') F", "alternative_algorithms": ["r' R2 U R' U r U2 r' U M'"], "move_count": 14, "difficulty": 3, "description": "No edges oriented - dot case", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_4", "name": "Dot Case 4", "case_name": "4", "category": "<PERSON>", "algorithm": "y' f (R U R' U') f' (U) F (R U R' U') F", "alternative_algorithms": ["l L2 U' L U' l' U2 l U' M'"], "move_count": 14, "difficulty": 3, "description": "No edges oriented - dot case", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_5", "name": "Square Shape 5", "case_name": "5", "category": "Square Shape", "algorithm": "r' U2 (R U R' U) r", "alternative_algorithms": ["y2 l' U2 L U L' U l"], "move_count": 7, "difficulty": 1, "description": "Square shape pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_6", "name": "Square Shape 6", "case_name": "6", "category": "Square Shape", "algorithm": "r <PERSON> (R' U' R U') r'", "alternative_algorithms": ["y2 l U2 L' U' L U' l'"], "move_count": 7, "difficulty": 1, "description": "Square shape pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_7", "name": "Lightning Shape 7", "case_name": "7", "category": "Lightning Shape", "algorithm": "r (U R' U R) U2 r'", "alternative_algorithms": ["r U r' U R U' R' r U' r'"], "move_count": 7, "difficulty": 1, "description": "Lightning bolt shape", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_8", "name": "Lightning Shape 8", "case_name": "8", "category": "Lightning Shape", "algorithm": "y2 r' (U' R U' R') U2 r", "alternative_algorithms": ["l' U' L U' L' U2 l"], "move_count": 8, "difficulty": 2, "description": "Lightning bolt shape", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_9", "name": "Fish Shape 9", "case_name": "9", "category": "Fish Shape", "algorithm": "y (R U R' U') (R' F R) (R U R' U') F'", "alternative_algorithms": ["R U2 R' U' S' R U' R' S"], "move_count": 13, "difficulty": 2, "description": "Fish shape pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_10", "name": "Fish Shape 10", "case_name": "10", "category": "Fish Shape", "algorithm": "(R U R' U) (R' F R F') (R U2 R')", "alternative_algorithms": ["y F U F' R' F R U' R' F' R"], "move_count": 11, "difficulty": 2, "description": "Fish shape pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_11", "name": "Lightning Shape 11", "case_name": "11", "category": "Lightning Shape", "algorithm": "M (R U R' U R U2 R') U M'", "alternative_algorithms": ["y2 r U R' U R' F R F' R U2 r'"], "move_count": 10, "difficulty": 2, "description": "Lightning bolt shape", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_12", "name": "Lightning Shape 12", "case_name": "12", "category": "Lightning Shape", "algorithm": "y' M' (<PERSON>' <PERSON>' R U' R' U2 R) U' M", "alternative_algorithms": ["y l L2 U' L U' L' U2 L U' M'"], "move_count": 11, "difficulty": 2, "description": "Lightning bolt shape", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_13", "name": "Knight Move Shape 13", "case_name": "13", "category": "Knight Move Shape", "algorithm": "(r U' r') U' (r U r') (F' U F)", "alternative_algorithms": ["F U R U' R2 F' R U R U' R'"], "move_count": 10, "difficulty": 2, "description": "Knight move pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_14", "name": "Knight Move Shape 14", "case_name": "14", "category": "Knight Move Shape", "algorithm": "R' F (R U R') F' R (F U' F')", "alternative_algorithms": ["r U R' U' r' F R2 U R' U' F'"], "move_count": 10, "difficulty": 2, "description": "Knight move pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_15", "name": "Knight Move Shape 15", "case_name": "15", "category": "Knight Move Shape", "algorithm": "(r' U' r) (R' U' R U) (r' U r)", "alternative_algorithms": ["y2 l' U' l L' U' L U l' U l"], "move_count": 10, "difficulty": 2, "description": "Knight move pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_16", "name": "Knight Move Shape 16", "case_name": "16", "category": "Knight Move Shape", "algorithm": "(r U r') (R U R' U') (r U' r')", "alternative_algorithms": ["y2 R' F R U R' U' F' R U' R' U2 R"], "move_count": 10, "difficulty": 2, "description": "Knight move pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_17", "name": "Dot Case 17", "case_name": "17", "category": "<PERSON>", "algorithm": "(R U R' U) (R' F R F') U2 (R' F R F')", "alternative_algorithms": ["y2 F R' F' R U S' R U' R' S"], "move_count": 13, "difficulty": 2, "description": "No edges oriented - dot case", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_18", "name": "Dot Case 18", "case_name": "18", "category": "<PERSON>", "algorithm": "y (R U2 R') (R' F R F') U2 M' (U R U' r')", "alternative_algorithms": ["r U R' U R U2 r2 U' R U' R' U2 r"], "move_count": 14, "difficulty": 3, "description": "No edges oriented - dot case", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_19", "name": "Dot Case 19", "case_name": "19", "category": "<PERSON>", "algorithm": "M U (R U R' U') M' (R' F R F')", "alternative_algorithms": ["r' R U R U R' U' r R2 F R F'"], "move_count": 11, "difficulty": 2, "description": "No edges oriented - dot case", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_20", "name": "Dot Case 20", "case_name": "20", "category": "<PERSON>", "algorithm": "(r U R' U') M2 (U R U' R') U' M", "alternative_algorithms": ["M' U2 M U2 M' U M U2 M' U2 M"], "move_count": 11, "difficulty": 2, "description": "No edges oriented - dot case", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_21", "name": "OCLL 21", "case_name": "21", "category": "OCLL", "algorithm": "(R U R' U) (R U' R' U) (R U2 R')", "alternative_algorithms": ["y F R U R' U' R U R' U' R U R' U' F'"], "move_count": 11, "difficulty": 2, "description": "All corners oriented", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_22", "name": "OCLL 22", "case_name": "22", "category": "OCLL", "algorithm": "R U2 (R2' U') (R2 U') (R2' U') U' R", "alternative_algorithms": ["R' U2 R2 U R2 U R2 U2 R'"], "move_count": 10, "difficulty": 1, "description": "All corners oriented", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_23", "name": "OCLL 23", "case_name": "23", "category": "OCLL", "algorithm": "R2 D (R' U2 R) D' (R' U2 R')", "alternative_algorithms": ["y2 R2 D' R U2 R' D R U2 R"], "move_count": 9, "difficulty": 2, "description": "All corners oriented", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_24", "name": "OCLL 24", "case_name": "24", "category": "OCLL", "algorithm": "(r U R' U') (r' F R F')", "alternative_algorithms": ["L F R' F' L' F R F'"], "move_count": 8, "difficulty": 1, "description": "All corners oriented", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_25", "name": "OCLL 25", "case_name": "25", "category": "OCLL", "algorithm": "y (F' r U R') (U' r' F R)", "alternative_algorithms": ["R U2 R D R' U2 R D' R2"], "move_count": 9, "difficulty": 2, "description": "All corners oriented", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_26", "name": "OCLL 26", "case_name": "26", "category": "OCLL", "algorithm": "y R <PERSON> (R' U' R U') R'", "alternative_algorithms": ["R' U' R U' R' U2 R"], "move_count": 8, "difficulty": 2, "description": "All corners oriented", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_27", "name": "OCLL 27", "case_name": "27", "category": "OCLL", "algorithm": "(R U R' U) (R U2 R')", "alternative_algorithms": ["y2 L U L' U L U2 L'"], "move_count": 7, "difficulty": 1, "description": "All corners oriented", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_28", "name": "All Corners Oriented 28", "case_name": "28", "category": "All Corners Oriented", "algorithm": "(r U R' U') M (U R U' R')", "alternative_algorithms": ["R' F R S R' F' R S'"], "move_count": 9, "difficulty": 1, "description": "All corners oriented", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_29", "name": "Awkward <PERSON> 29", "case_name": "29", "category": "<PERSON><PERSON><PERSON><PERSON>", "algorithm": "y (R U R') U' (R U' R') (F' U' F) (R U R')", "alternative_algorithms": ["r2 D' r U r' D r2 U' r' U' r"], "move_count": 14, "difficulty": 3, "description": "Awkward shape pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_30", "name": "Awkward Shape 30", "case_name": "30", "category": "<PERSON><PERSON><PERSON><PERSON>", "algorithm": "y2 F U (R U2 R') U' (R U2 R') U' F'", "alternative_algorithms": ["y2 F R' F R2 U' R' U' R U R' F2"], "move_count": 12, "difficulty": 2, "description": "Awkward shape pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_31", "name": "P Shapes 31", "case_name": "31", "category": "<PERSON>", "algorithm": "(R' U' F) (U R U' R') F' R", "alternative_algorithms": ["y' F R' F' R U R U R' U' R U' R'"], "move_count": 9, "difficulty": 1, "description": "P shape pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_32", "name": "P Shapes 32", "case_name": "32", "category": "<PERSON>", "algorithm": "S (R U R' U') (R' F R f')", "alternative_algorithms": ["y2 L U F' U' L' U L F L'"], "move_count": 9, "difficulty": 2, "description": "P shape pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_33", "name": "T Shapes 33", "case_name": "33", "category": "<PERSON>", "algorithm": "(R U R' U') (R' F R F')", "alternative_algorithms": ["y2 L' U' L U L F' L' F"], "move_count": 8, "difficulty": 1, "description": "T shape pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_34", "name": "C Shape 34", "case_name": "34", "category": "C <PERSON>", "algorithm": "(r U r') (R U R' U') (r U' r')", "alternative_algorithms": ["y f R f' U' r' U' R U M'"], "move_count": 10, "difficulty": 2, "description": "C shape pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_35", "name": "Fish Shape 35", "case_name": "35", "category": "Fish Shape", "algorithm": "(R U2 R') (R' F R F') (R U2 R')", "alternative_algorithms": ["f R U R' U' f' R U R' U R U2 R'"], "move_count": 10, "difficulty": 2, "description": "Fish shape pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_36", "name": "<PERSON> <PERSON>hape 36", "case_name": "36", "category": "<PERSON>pe", "algorithm": "y2 (L' U' L U') (L' U L U) (L F' L' F)", "alternative_algorithms": ["y2 L' U' L U' L' U L U L F' L' F"], "move_count": 13, "difficulty": 2, "description": "W shape pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_37", "name": "Fish Shape 37", "case_name": "37", "category": "Fish Shape", "algorithm": "F R (U' R' U') (R U R') F'", "alternative_algorithms": ["F R U' R' U' R U R' F'"], "move_count": 9, "difficulty": 1, "description": "Fish shape pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_38", "name": "<PERSON> <PERSON><PERSON><PERSON> 38", "case_name": "38", "category": "<PERSON>pe", "algorithm": "(R U R' U) (R U' R' U') (R' F R F')", "alternative_algorithms": ["y2 L' U2 l' D' l U2 l' D l L"], "move_count": 12, "difficulty": 2, "description": "W shape pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_39", "name": "Lightning Shape 39", "case_name": "39", "category": "Lightning Shape", "algorithm": "y L F' (L' U' L U) F U' L'", "alternative_algorithms": ["y' f' L F L' U' L' U L S"], "move_count": 10, "difficulty": 2, "description": "Lightning bolt shape", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_40", "name": "Lightning Shape 40", "case_name": "40", "category": "Lightning Shape", "algorithm": "y R' F (R U R' U') F' U R", "alternative_algorithms": ["y' f R' F' R U R U' R' S'"], "move_count": 10, "difficulty": 2, "description": "Lightning bolt shape", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_41", "name": "Awkward <PERSON> 41", "case_name": "41", "category": "<PERSON><PERSON><PERSON><PERSON>", "algorithm": "y2 (R U R' U) (R U2 R') F (R U R' U') F'", "alternative_algorithms": ["y2 F U R2 D R' U' R D' R2 F'"], "move_count": 14, "difficulty": 3, "description": "Awkward shape pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_42", "name": "Awkward <PERSON> 42", "case_name": "42", "category": "<PERSON><PERSON><PERSON><PERSON>", "algorithm": "(R' U' R U') (R' U2 R) F (R U R' U') F'", "alternative_algorithms": ["y F R' F' R U2 R' U' R2 U' R2 U2 R"], "move_count": 13, "difficulty": 2, "description": "Awkward shape pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_43", "name": "<PERSON> 43", "case_name": "43", "category": "<PERSON>", "algorithm": "y R' U' (F' U F) R", "alternative_algorithms": ["y2 F' U' L' U L F"], "move_count": 7, "difficulty": 2, "description": "P shape pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_44", "name": "<PERSON> 44", "case_name": "44", "category": "<PERSON>", "algorithm": "f (R U R' U') f'", "alternative_algorithms": ["y2 F U R U' R' F'"], "move_count": 6, "difficulty": 1, "description": "P shape pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_45", "name": "T Shapes 45", "case_name": "45", "category": "<PERSON>", "algorithm": "F (R U R' U') F'", "alternative_algorithms": ["y2 F' L' U' L U F"], "move_count": 6, "difficulty": 1, "description": "T shape pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_46", "name": "C Shape 46", "case_name": "46", "category": "C <PERSON>", "algorithm": "R' U' (R' F R F') U R", "alternative_algorithms": ["R' F' U' F R U' R' U2 R"], "move_count": 8, "difficulty": 1, "description": "C shape pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_47", "name": "L Shapes 47", "case_name": "47", "category": "<PERSON>", "algorithm": "F' (L' U' L U) (L' U' L U) F", "alternative_algorithms": ["y' F R' F' R U2 R U' R' U R U2 R'"], "move_count": 10, "difficulty": 2, "description": "L shape pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_48", "name": "L Shapes 48", "case_name": "48", "category": "<PERSON>", "algorithm": "F (R U R' U') (R U R' U') F'", "alternative_algorithms": ["F R' F' U2 R U R' U R2 U2 R'"], "move_count": 10, "difficulty": 2, "description": "L shape pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_49", "name": "L Shapes 49", "case_name": "49", "category": "<PERSON>", "algorithm": "y2 r U' (r2 U) (r2 U) (r2) U' r", "alternative_algorithms": ["l U' l2 U l2 U l2 U' l"], "move_count": 10, "difficulty": 2, "description": "L shape pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_50", "name": "L Shapes 50", "case_name": "50", "category": "<PERSON>", "algorithm": "r' U (r2 U') (r2 U') (r2) U r'", "alternative_algorithms": ["y2 l' U l2 U' l2 U' l2 U l'"], "move_count": 9, "difficulty": 1, "description": "L shape pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_51", "name": "Line Shapes 51", "case_name": "51", "category": "Line Shapes", "algorithm": "f (R U R' U') (R U R' U') f'", "alternative_algorithms": ["F' U' L' U L U' L' U L F"], "move_count": 10, "difficulty": 2, "description": "Line shape pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_52", "name": "Line Shapes 52", "case_name": "52", "category": "Line Shapes", "algorithm": "y2 R' (F' U' F U') (R U R' U) R", "alternative_algorithms": ["R U R' U R U' B U' B' R'"], "move_count": 11, "difficulty": 2, "description": "Line shape pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_53", "name": "L Shapes 53", "case_name": "53", "category": "<PERSON>", "algorithm": "(r' U' R U') (R' U R U') (R' U2 r)", "alternative_algorithms": ["y2 l' U' L U' L' U L U' L' U2 l"], "move_count": 11, "difficulty": 2, "description": "L shape pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_54", "name": "L Shapes 54", "case_name": "54", "category": "<PERSON>", "algorithm": "(r U R' U) (R U' R' U) (R U2 r')", "alternative_algorithms": ["y2 l U L' U L U' L' U L U2 l'"], "move_count": 11, "difficulty": 2, "description": "L shape pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_55", "name": "Line Shapes 55", "case_name": "55", "category": "Line Shapes", "algorithm": "R U2 R2 (U' R U' R') U2 (F R F')", "alternative_algorithms": ["y R' F U R U' R2 F' R2 U R' U' R"], "move_count": 11, "difficulty": 2, "description": "Line shape pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_56", "name": "Line Shapes 56", "case_name": "56", "category": "Line Shapes", "algorithm": "(r U r') (U R U' R') (U R U' R') (r U' r')", "alternative_algorithms": ["F R U R' U' R F' r U R' U' r'"], "move_count": 14, "difficulty": 2, "description": "Line shape pattern", "source": "https://speedcubedb.com/a/3x3/OLL"}, {"id": "oll_57", "name": "All Corners Oriented 57", "case_name": "57", "category": "All Corners Oriented", "algorithm": "(R U R' U') M' (U R U' r')", "alternative_algorithms": ["y R U' R' S' R U R' S"], "move_count": 9, "difficulty": 2, "description": "All corners oriented", "source": "https://speedcubedb.com/a/3x3/OLL"}]}, "PLL": {"name": "Permutation of Last Layer", "description": "Algorithms for permuting all pieces on the last layer", "cases": [{"id": "pll_aa", "name": "Aa Perm", "case_name": "Aa Perm", "category": "A Perm", "algorithm": "x (R' U R') D2 (R U' R') D2 R2 x'", "alternative_algorithms": [], "move_count": 11, "difficulty": 3, "description": "Clockwise corner 3-cycle", "source": "https://speedcubedb.com/a/3x3/PLL"}, {"id": "pll_ab", "name": "<PERSON><PERSON>", "case_name": "<PERSON><PERSON>", "category": "A Perm", "algorithm": "x R2 D2 (R U R') D2 (R U' R) x'", "alternative_algorithms": [], "move_count": 11, "difficulty": 3, "description": "Counter-clockwise corner 3-cycle", "source": "https://speedcubedb.com/a/3x3/PLL"}, {"id": "pll_e", "name": "E Perm", "case_name": "E Perm", "category": "E Perm", "algorithm": "y x' (R U' R' D) (R U R' D') (R U R' D) (R U' R' D') x", "alternative_algorithms": [], "move_count": 19, "difficulty": 4, "description": "Diagonal corner swap + diagonal edge swap", "source": "https://speedcubedb.com/a/3x3/PLL"}, {"id": "pll_f", "name": "F Perm", "case_name": "F Perm", "category": "F Perm", "algorithm": "y (R' U' F') (R U R' U') R' F R2 (U' R' U') (R U R' U) R", "alternative_algorithms": [], "move_count": 19, "difficulty": 3, "description": "Adjacent corner swap + adjacent edge swap", "source": "https://speedcubedb.com/a/3x3/PLL"}, {"id": "pll_ga", "name": "Ga Perm", "case_name": "Ga Perm", "category": "G Perm", "algorithm": "R2 (U R' U R' U' R U') R2 D (U' R' U R) D'", "alternative_algorithms": ["y R U R' F' R U R' U' R' F R U' R' F R2 U' R' U' R U R' F'"], "move_count": 15, "difficulty": 2, "description": "Adjacent corner swap + diagonal edge swap", "source": "https://speedcubedb.com/a/3x3/PLL"}, {"id": "pll_gb", "name": "Gb Perm", "case_name": "Gb Perm", "category": "G Perm", "algorithm": "(R' U' R U) D' R2 (U R' U R U' R U') R2 D", "alternative_algorithms": [], "move_count": 15, "difficulty": 2, "description": "Adjacent corner swap + diagonal edge swap", "source": "https://speedcubedb.com/a/3x3/PLL"}, {"id": "pll_gc", "name": "Gc Perm", "case_name": "Gc Perm", "category": "G Perm", "algorithm": "R2 (U' R U' R U R' U) R2 D' (U R U' R') D", "alternative_algorithms": ["y2 R2 F2 R U2 R U2 R' F R U R' U' R' F R2"], "move_count": 15, "difficulty": 2, "description": "Adjacent corner swap + diagonal edge swap", "source": "https://speedcubedb.com/a/3x3/PLL"}, {"id": "pll_gd", "name": "Gd Perm", "case_name": "Gd Perm", "category": "G Perm", "algorithm": "(R U R' U') D R2 (U' R U' R' U R' U) R2 D'", "alternative_algorithms": [], "move_count": 15, "difficulty": 2, "description": "Adjacent corner swap + diagonal edge swap", "source": "https://speedcubedb.com/a/3x3/PLL"}, {"id": "pll_h", "name": "H Perm", "case_name": "H Perm", "category": "H Perm", "algorithm": "(M2 U' M2) U2 (M2 U' M2)", "alternative_algorithms": [], "move_count": 7, "difficulty": 1, "description": "Opposite edge swap", "source": "https://speedcubedb.com/a/3x3/PLL"}, {"id": "pll_ja", "name": "<PERSON><PERSON>", "case_name": "<PERSON><PERSON>", "category": "J Perm", "algorithm": "y (R' U L') U2 (R U' R') U2 R L", "alternative_algorithms": ["y R' U L' U2 R U' R' U2 R L"], "move_count": 11, "difficulty": 2, "description": "Adjacent corner swap", "source": "https://speedcubedb.com/a/3x3/PLL"}, {"id": "pll_jb", "name": "<PERSON><PERSON>", "case_name": "<PERSON><PERSON>", "category": "J Perm", "algorithm": "(R U R' F') (R U R' U') R' F R2 U' R'", "alternative_algorithms": [], "move_count": 13, "difficulty": 2, "description": "Adjacent corner swap", "source": "https://speedcubedb.com/a/3x3/PLL"}, {"id": "pll_na", "name": "Na Perm", "case_name": "Na Perm", "category": "N Perm", "algorithm": "(R U R' U) (R U R' F') (R U R' U') R' F R2 U' R' U2 (R U' R')", "alternative_algorithms": [], "move_count": 21, "difficulty": 3, "description": "Adjacent corner swap + diagonal edge swap", "source": "https://speedcubedb.com/a/3x3/PLL"}, {"id": "pll_nb", "name": "Nb Perm", "case_name": "Nb Perm", "category": "N Perm", "algorithm": "(R' U R U' R') (F' U' F) (R U R') (F R' F') (R U' R)", "alternative_algorithms": ["r' D' F r U' r' F' D r2 U r' U' r' F r F'"], "move_count": 17, "difficulty": 3, "description": "Adjacent corner swap + diagonal edge swap", "source": "https://speedcubedb.com/a/3x3/PLL"}, {"id": "pll_ra", "name": "Ra <PERSON>", "case_name": "Ra <PERSON>", "category": "R Perm", "algorithm": "y (R U' R' U') (R U R D) (R' U' R D') (R' U2 R')", "alternative_algorithms": [], "move_count": 16, "difficulty": 3, "description": "Adjacent corner swap + adjacent edge swap", "source": "https://speedcubedb.com/a/3x3/PLL"}, {"id": "pll_rb", "name": "Rb <PERSON>", "case_name": "Rb <PERSON>", "category": "R Perm", "algorithm": "(R' <PERSON>) (R U2) (R' F R) (U R' U' R') F' R2", "alternative_algorithms": ["y R2 F R U R U' R' F' R U2 R' U2 R"], "move_count": 13, "difficulty": 2, "description": "Adjacent corner swap + adjacent edge swap", "source": "https://speedcubedb.com/a/3x3/PLL"}, {"id": "pll_t", "name": "T Perm", "case_name": "T Perm", "category": "T Perm", "algorithm": "(R U R' U') (R' F R2) (U' R' U') (R U R' F')", "alternative_algorithms": [], "move_count": 14, "difficulty": 2, "description": "Adjacent corner swap + opposite edge swap", "source": "https://speedcubedb.com/a/3x3/PLL"}, {"id": "pll_ua", "name": "Ua Perm", "case_name": "Ua Perm", "category": "U Perm", "algorithm": "y2 (M2 U M) U2 (M' U M2)", "alternative_algorithms": ["R U R' U R' U' R2 U' R' U R' U R"], "move_count": 8, "difficulty": 2, "description": "Clockwise edge 3-cycle", "source": "https://speedcubedb.com/a/3x3/PLL"}, {"id": "pll_ub", "name": "Ub Perm", "case_name": "Ub Perm", "category": "U Perm", "algorithm": "y2 (M2 U' M) U2 (M' U' M2)", "alternative_algorithms": ["R2' U R U R' U' R3 U' R' U R'"], "move_count": 8, "difficulty": 2, "description": "Counter-clockwise edge 3-cycle", "source": "https://speedcubedb.com/a/3x3/PLL"}, {"id": "pll_v", "name": "V Perm", "case_name": "V Perm", "category": "V Perm", "algorithm": "(R' U R' U') (R D' R' D) (R' U D') (R2 U' R2) D R2", "alternative_algorithms": ["R' U R U' x' U R U2 R' U' R U' R' U2 R U R' U'"], "move_count": 16, "difficulty": 3, "description": "Diagonal corner swap + adjacent edge swap", "source": "https://speedcubedb.com/a/3x3/PLL"}, {"id": "pll_y", "name": "Y Perm", "case_name": "Y Perm", "category": "Y Perm", "algorithm": "F R (U' R' U') (R U R' F') (R U R' U') (R' F R F')", "alternative_algorithms": [], "move_count": 17, "difficulty": 3, "description": "Diagonal corner swap + diagonal edge swap", "source": "https://speedcubedb.com/a/3x3/PLL"}, {"id": "pll_z", "name": "Z Perm", "case_name": "Z Perm", "category": "Z Perm", "algorithm": "(M2 U) (M2 U) (M' U2) M2 (U2 M')", "alternative_algorithms": [], "move_count": 9, "difficulty": 2, "description": "Diagonal edge swap", "source": "https://speedcubedb.com/a/3x3/PLL"}]}, "F2L": {"name": "First Two Layers", "description": "Algorithms for solving the first two layers simultaneously", "cases": [{"id": "f2l_1", "name": "Case 1", "case_name": "1", "category": "Basic Insertion", "difficulty": 1, "description": "Basic corner-edge pair insertion", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "U R U' R'", "alternative_algorithms": [], "move_count": 4}, "Front-Left": {"algorithm": "F' r U r'", "alternative_algorithms": [], "move_count": 4}, "Back-Left": {"algorithm": "U L U' L'", "alternative_algorithms": [], "move_count": 4}, "Back-Right": {"algorithm": "U f R' f'", "alternative_algorithms": [], "move_count": 4}}}, {"id": "f2l_2", "name": "Case 2", "case_name": "2", "category": "Basic Insertion", "difficulty": 1, "description": "Corner and edge separated", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "F R' F' R", "alternative_algorithms": [], "move_count": 4}, "Front-Left": {"algorithm": "U' L' U L", "alternative_algorithms": [], "move_count": 4}, "Back-Left": {"algorithm": "l U L' U' M'", "alternative_algorithms": [], "move_count": 5}, "Back-Right": {"algorithm": "U' R' U R", "alternative_algorithms": [], "move_count": 4}}}, {"id": "f2l_3", "name": "Case 3", "case_name": "3", "category": "Corner Oriented", "difficulty": 1, "description": "Corner oriented, edge in top", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "F' U' F", "alternative_algorithms": [], "move_count": 3}, "Front-Left": {"algorithm": "L' U' L", "alternative_algorithms": [], "move_count": 3}, "Back-Left": {"algorithm": "y R' U' R", "alternative_algorithms": [], "move_count": 4}, "Back-Right": {"algorithm": "R' U' R", "alternative_algorithms": [], "move_count": 3}}}, {"id": "f2l_4", "name": "Case 4", "case_name": "4", "category": "Corner Oriented", "difficulty": 1, "description": "Corner oriented, edge in top", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "R U R'", "alternative_algorithms": [], "move_count": 3}, "Front-Left": {"algorithm": "F U F'", "alternative_algorithms": [], "move_count": 3}, "Back-Left": {"algorithm": "L U L", "alternative_algorithms": [], "move_count": 3}, "Back-Right": {"algorithm": "f R f'", "alternative_algorithms": [], "move_count": 3}}}, {"id": "f2l_5", "name": "Case 5", "case_name": "5", "category": "Edge Oriented Wrong", "difficulty": 1, "description": "Edge oriented wrong", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "U' R U R' U2 R U' R'", "alternative_algorithms": [], "move_count": 8}, "Front-Left": {"algorithm": "U R' F r U' r' F' R", "alternative_algorithms": [], "move_count": 8}, "Back-Left": {"algorithm": "U' L U L' U2 L U' L'", "alternative_algorithms": [], "move_count": 8}, "Back-Right": {"algorithm": "U' R' F R U R' U' F' R", "alternative_algorithms": [], "move_count": 9}}}, {"id": "f2l_6", "name": "Case 6", "case_name": "6", "category": "Edge Oriented Wrong", "difficulty": 1, "description": "Edge oriented wrong", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "U' r U' R' U R U r'", "alternative_algorithms": [], "move_count": 8}, "Front-Left": {"algorithm": "U L' U' L U2 L' U L", "alternative_algorithms": [], "move_count": 8}, "Back-Left": {"algorithm": "U r U' r' U' L U F L'", "alternative_algorithms": [], "move_count": 9}, "Back-Right": {"algorithm": "U R' U' R U2 R' U R", "alternative_algorithms": [], "move_count": 8}}}, {"id": "f2l_7", "name": "Case 7", "case_name": "7", "category": "Corner Oriented Wrong", "difficulty": 1, "description": "Corner oriented wrong", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "U' R U2 R' U' R U2 R'", "alternative_algorithms": [], "move_count": 8}, "Front-Left": {"algorithm": "F U R U2 R' U F'", "alternative_algorithms": [], "move_count": 7}, "Back-Left": {"algorithm": "U' L U2 L' U2 L U' L", "alternative_algorithms": [], "move_count": 8}, "Back-Right": {"algorithm": "r U2 R2 U' R2 U' r", "alternative_algorithms": [], "move_count": 7}}}, {"id": "f2l_8", "name": "Case 8", "case_name": "8", "category": "Corner Oriented Wrong", "difficulty": 1, "description": "Corner oriented wrong", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "d R' U2 R U R' U2 R", "alternative_algorithms": [], "move_count": 8}, "Front-Left": {"algorithm": "U L' U2 L U L' U2 L", "alternative_algorithms": [], "move_count": 8}, "Back-Left": {"algorithm": "l' U2 L2 U L2 U l", "alternative_algorithms": [], "move_count": 7}, "Back-Right": {"algorithm": "U R' U2 R U R' U2 R", "alternative_algorithms": [], "move_count": 8}}}, {"id": "f2l_9", "name": "Case 9", "case_name": "9", "category": "Weird", "difficulty": 1, "description": "Weird case", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "U' R U' R' U F' U' F", "alternative_algorithms": [], "move_count": 8}, "Front-Left": {"algorithm": "U L' U' L U' L' U' L", "alternative_algorithms": [], "move_count": 8}, "Back-Left": {"algorithm": "y U R' U' R U' R' U' R", "alternative_algorithms": [], "move_count": 9}, "Back-Right": {"algorithm": "U R' U' R U' R' U' R", "alternative_algorithms": [], "move_count": 8}}}, {"id": "f2l_10", "name": "Case 10", "case_name": "10", "category": "Weird", "difficulty": 1, "description": "Weird case", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "U' R U R' U R U R'", "alternative_algorithms": [], "move_count": 8}, "Front-Left": {"algorithm": "U L' U L U' F U F", "alternative_algorithms": [], "move_count": 8}, "Back-Left": {"algorithm": "U' L U L' U L U L'", "alternative_algorithms": [], "move_count": 8}, "Back-Right": {"algorithm": "U R' U R U' f R f'", "alternative_algorithms": [], "move_count": 8}}}, {"id": "f2l_11", "name": "Case 11", "case_name": "11", "category": "Corner Right Edge Wrong", "difficulty": 1, "description": "Corner right, edge wrong", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "U' R U2 R' U F' U' F", "alternative_algorithms": [], "move_count": 8}, "Front-Left": {"algorithm": "L' U L U' L' U L U2 L' U L", "alternative_algorithms": [], "move_count": 11}, "Back-Left": {"algorithm": "U' L U2 L' U f' L' f", "alternative_algorithms": [], "move_count": 8}, "Back-Right": {"algorithm": "R' U R U' R' U R U2 R' U R", "alternative_algorithms": [], "move_count": 11}}}, {"id": "f2l_12", "name": "Case 12", "case_name": "12", "category": "Corner Right Edge Wrong", "difficulty": 2, "description": "Corner right, edge wrong", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "R U' R' U R U' R' U2 R U' R'", "alternative_algorithms": [], "move_count": 11}, "Front-Left": {"algorithm": "U L' U2 L U' F U F'", "alternative_algorithms": [], "move_count": 8}, "Back-Left": {"algorithm": "L' U2 L2 U L2 U L", "alternative_algorithms": [], "move_count": 7}, "Back-Right": {"algorithm": "U R' U2 R U' f R f'", "alternative_algorithms": [], "move_count": 8}}}, {"id": "f2l_13", "name": "Case 13", "case_name": "13", "category": "Corner Wrong Edge Right", "difficulty": 1, "description": "Corner wrong, edge right", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "y' U R' U R U' R' U' R", "alternative_algorithms": [], "move_count": 9}, "Front-Left": {"algorithm": "U L' U L U' L' U' L", "alternative_algorithms": [], "move_count": 8}, "Back-Left": {"algorithm": "d L' U L U' L' U' L", "alternative_algorithms": [], "move_count": 8}, "Back-Right": {"algorithm": "U R' U R U' R' U' R", "alternative_algorithms": [], "move_count": 8}}}, {"id": "f2l_14", "name": "Case 14", "case_name": "14", "category": "Corner Wrong Edge Right", "difficulty": 1, "description": "Corner wrong, edge right", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "U' R U' R' U R U R'", "alternative_algorithms": [], "move_count": 8}, "Front-Left": {"algorithm": "d' L U' L' U L U L'", "alternative_algorithms": [], "move_count": 8}, "Back-Left": {"algorithm": "U' L U' L' U L U L'", "alternative_algorithms": [], "move_count": 8}, "Back-Right": {"algorithm": "y U' R U' R' U R U R'", "alternative_algorithms": [], "move_count": 9}}}, {"id": "f2l_15", "name": "Case 15", "case_name": "15", "category": "Both Wrong", "difficulty": 2, "description": "Both corner and edge wrong", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "R' D' R U' R' D R U R U' R'", "alternative_algorithms": [], "move_count": 11}, "Front-Left": {"algorithm": "L' U L U2 F U F'", "alternative_algorithms": [], "move_count": 7}, "Back-Left": {"algorithm": "L U L' U2 L U' L' U L U' L'", "alternative_algorithms": [], "move_count": 11}, "Back-Right": {"algorithm": "R' U R U2 f R f'", "alternative_algorithms": [], "move_count": 7}}}, {"id": "f2l_16", "name": "Case 16", "case_name": "16", "category": "Both Wrong", "difficulty": 2, "description": "Both corner and edge wrong", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "R U' R' U2 R U R'", "alternative_algorithms": [], "move_count": 7}, "Front-Left": {"algorithm": "L D L' U L D' L' U' L' U L", "alternative_algorithms": [], "move_count": 11}, "Back-Left": {"algorithm": "L' U L U2 L' U' L", "alternative_algorithms": [], "move_count": 7}, "Back-Right": {"algorithm": "R U' R' U2 R U R' U' R' U R", "alternative_algorithms": [], "move_count": 11}}}, {"id": "f2l_17", "name": "Case 17", "case_name": "17", "category": "Pair <PERSON>", "difficulty": 1, "description": "Pair made on top", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "R U R' F R F'", "alternative_algorithms": [], "move_count": 6}, "Front-Left": {"algorithm": "L' U' L F' L' F", "alternative_algorithms": [], "move_count": 6}, "Back-Left": {"algorithm": "y L' U' L F' L' F", "alternative_algorithms": [], "move_count": 7}, "Back-Right": {"algorithm": "y' R U R' F R F'", "alternative_algorithms": [], "move_count": 7}}}, {"id": "f2l_18", "name": "Case 18", "case_name": "18", "category": "Pair <PERSON>", "difficulty": 1, "description": "Pair made on top", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "F R' F' R", "alternative_algorithms": [], "move_count": 4}, "Front-Left": {"algorithm": "F' L F L'", "alternative_algorithms": [], "move_count": 4}, "Back-Left": {"algorithm": "y F' L F L'", "alternative_algorithms": [], "move_count": 5}, "Back-Right": {"algorithm": "y' F R' F' R", "alternative_algorithms": [], "move_count": 5}}}, {"id": "f2l_19", "name": "Case 19", "case_name": "19", "category": "Pair <PERSON>", "difficulty": 1, "description": "Pair made on top", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "R U' R' F R F'", "alternative_algorithms": [], "move_count": 6}, "Front-Left": {"algorithm": "L' U L F' L' F", "alternative_algorithms": [], "move_count": 6}, "Back-Left": {"algorithm": "y L' U L F' L' F", "alternative_algorithms": [], "move_count": 7}, "Back-Right": {"algorithm": "y' R U' R' F R F'", "alternative_algorithms": [], "move_count": 7}}}, {"id": "f2l_20", "name": "Case 20", "case_name": "20", "category": "Pair <PERSON>", "difficulty": 1, "description": "Pair made on top", "source": "https://speedcubedb.com/a/3x3/F2L", "slots": {"Front-Right": {"algorithm": "R U R' U' F R F'", "alternative_algorithms": [], "move_count": 7}, "Front-Left": {"algorithm": "L' U' L U F' L' F", "alternative_algorithms": [], "move_count": 7}, "Back-Left": {"algorithm": "y L' U' L U F' L' F", "alternative_algorithms": [], "move_count": 8}, "Back-Right": {"algorithm": "y' R U R' U' F R F'", "alternative_algorithms": [], "move_count": 8}}}]}}}}}}