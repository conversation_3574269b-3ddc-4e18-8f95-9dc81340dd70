{"3x3x3 Cube": {"name": "3x3x3 Cube", "category": "NxNxN Cube", "event_type": "WCA", "methods": {"CFOP": {"name": "CFOP", "description": "Cross, F2L, OLL, PLL - The most popular speedcubing method", "steps": {"F2L": {"name": "First Two Layers", "description": "Algorithms for solving the first two layers simultaneously", "cases": [{"id": "f2l_1", "name": "Case 1", "description": "Basic corner-edge pair insertion", "difficulty": 1, "slots": {"Front-Right": {"algorithm": "U R U' R'", "alternative_algorithms": [], "move_count": 4}, "Front-Left": {"algorithm": "F' r U r'", "alternative_algorithms": [], "move_count": 4}, "Back-Left": {"algorithm": "U L U' L'", "alternative_algorithms": [], "move_count": 4}, "Back-Right": {"algorithm": "U f R' f'", "alternative_algorithms": [], "move_count": 4}}}, {"id": "f2l_2", "name": "Case 2", "description": "Corner and edge separated", "difficulty": 1, "slots": {"Front-Right": {"algorithm": "F R' F' R", "alternative_algorithms": [], "move_count": 4}, "Front-Left": {"algorithm": "U' L' U L", "alternative_algorithms": [], "move_count": 4}, "Back-Left": {"algorithm": "l U L' U' M'", "alternative_algorithms": [], "move_count": 5}, "Back-Right": {"algorithm": "U' R' U R", "alternative_algorithms": [], "move_count": 4}}}, {"id": "f2l_3", "name": "Case 3", "description": "Corner in place, edge in top", "difficulty": 1, "slots": {"Front-Right": {"algorithm": "F' U' F", "alternative_algorithms": [], "move_count": 3}, "Front-Left": {"algorithm": "L' U' L", "alternative_algorithms": [], "move_count": 3}, "Back-Left": {"algorithm": "y R' U' R", "alternative_algorithms": [], "move_count": 4}, "Back-Right": {"algorithm": "R' U' R", "alternative_algorithms": [], "move_count": 3}}}]}, "OLL": {"name": "Orientation of Last Layer", "description": "Algorithms for orienting all pieces on the last layer", "cases": [{"id": "oll_1", "name": "Dot Case 1", "algorithm": "(R U2 R') (R' F R F') U2 (R' F R F')", "alternative_algorithms": ["R' U' F R' F' R2 U R f' U' f"], "move_count": 12, "difficulty": 2, "description": "No edges oriented - dot case"}, {"id": "oll_2", "name": "Dot Case 2", "algorithm": "F (R U R' U') F' f (R U R' U') f'", "alternative_algorithms": ["r U r' U2 R U2 R' U2 r U' r'"], "move_count": 12, "difficulty": 2, "description": "No edges oriented - dot case"}, {"id": "oll_5", "name": "Square Shape 5", "algorithm": "r' U2 (R U R' U) r", "alternative_algorithms": ["l' U2 L U L' U l"], "move_count": 7, "difficulty": 1, "description": "Square shape pattern"}, {"id": "oll_6", "name": "Square Shape 6", "algorithm": "r <PERSON> (R' U' R U') r'", "alternative_algorithms": ["l U2 L' U' L U' l'"], "move_count": 7, "difficulty": 1, "description": "Square shape pattern"}, {"id": "oll_7", "name": "Lightning Shape 7", "algorithm": "r (U R' U R) U2 r'", "alternative_algorithms": ["r U r' U R U' R' r U' r'"], "move_count": 7, "difficulty": 1, "description": "Lightning bolt shape"}, {"id": "oll_8", "name": "Lightning Shape 8", "algorithm": "r' (U' R U' R') U2 r", "alternative_algorithms": ["l' U' L U' L' U2 l"], "move_count": 8, "difficulty": 2, "description": "Lightning bolt shape"}, {"id": "oll_21", "name": "OCLL 21", "algorithm": "(R U R' U) (R U' R' U) (R U2 R')", "alternative_algorithms": ["F R U R' U' R U R' U' R U R' U' F'"], "move_count": 11, "difficulty": 2, "description": "All corners oriented - H case"}, {"id": "oll_22", "name": "OCLL 22", "algorithm": "R U2 (R2' U') (R2 U') (R2' U') U' R", "alternative_algorithms": ["R' U2 R2 U R2 U R2 U2 R'"], "move_count": 10, "difficulty": 1, "description": "All corners oriented - Pi case"}, {"id": "oll_23", "name": "OCLL 23", "algorithm": "R2 D (R' U2 R) D' (R' U2 R')", "alternative_algorithms": ["R2 D' R U2 R' D R U2 R"], "move_count": 9, "difficulty": 2, "description": "All corners oriented - U case"}, {"id": "oll_24", "name": "OCLL 24", "algorithm": "(r U R' U') (r' F R F')", "alternative_algorithms": ["L F R' F' L' F R F'"], "move_count": 8, "difficulty": 1, "description": "All corners oriented - T case"}, {"id": "oll_33", "name": "T Shape 33", "algorithm": "(R U R' U') (R' F R F')", "alternative_algorithms": ["L' U' L U L F' L' F"], "move_count": 8, "difficulty": 1, "description": "T shape pattern"}, {"id": "oll_45", "name": "T Shape 45", "algorithm": "F (R U R' U') F'", "alternative_algorithms": ["F' L' U' L U F"], "move_count": 6, "difficulty": 1, "description": "T shape pattern"}, {"id": "oll_3", "name": "Dot Case 3", "algorithm": "f (R U R' U') f' (U') F (R U R' U') F", "alternative_algorithms": ["r' R2 U R' U r U2 r' U M'"], "move_count": 14, "difficulty": 3, "description": "No edges oriented - dot case"}, {"id": "oll_4", "name": "Dot Case 4", "algorithm": "f (R U R' U') f' (U) F (R U R' U') F", "alternative_algorithms": ["l L2 U' L U' l' U2 l U' M'"], "move_count": 14, "difficulty": 3, "description": "No edges oriented - dot case"}, {"id": "oll_9", "name": "Fish Shape 9", "algorithm": "(R U R' U') (R' F R) (R U R' U') F'", "alternative_algorithms": ["R U2 R' U' S' R U' R' S"], "move_count": 13, "difficulty": 2, "description": "Fish shape pattern"}, {"id": "oll_10", "name": "Fish Shape 10", "algorithm": "(R U R' U) (R' F R F') (R U2 R')", "alternative_algorithms": ["F U F' R' F R U' R' F' R"], "move_count": 11, "difficulty": 2, "description": "Fish shape pattern"}, {"id": "oll_11", "name": "Lightning Shape 11", "algorithm": "M (R U R' U R U2 R') U M'", "alternative_algorithms": ["r U R' U R' F R F' R U2 r'"], "move_count": 10, "difficulty": 2, "description": "Lightning bolt shape"}, {"id": "oll_12", "name": "Lightning Shape 12", "algorithm": "M' (<PERSON>' <PERSON>' R U' R' U2 R) U' M", "alternative_algorithms": ["l L2 U' L U' L' U2 L U' M'"], "move_count": 11, "difficulty": 2, "description": "Lightning bolt shape"}, {"id": "oll_13", "name": "Knight Move Shape 13", "algorithm": "(r U' r') U' (r U r') (F' U F)", "alternative_algorithms": ["F U R U' R2 F' R U R U' R'"], "move_count": 10, "difficulty": 2, "description": "Knight move shape"}, {"id": "oll_14", "name": "Knight Move Shape 14", "algorithm": "R' F (R U R') F' R (F U' F')", "alternative_algorithms": ["r U R' U' r' F R2 U R' U' F'"], "move_count": 10, "difficulty": 2, "description": "Knight move shape"}, {"id": "oll_15", "name": "Knight Move Shape 15", "algorithm": "(r' U' r) (R' U' R U) (r' U r)", "alternative_algorithms": ["l' U' l L' U' L U l' U l"], "move_count": 10, "difficulty": 2, "description": "Knight move shape"}, {"id": "oll_16", "name": "Knight Move Shape 16", "algorithm": "(r U r') (R U R' U') (r U' r')", "alternative_algorithms": ["R' F R U R' U' F' R U' R' U2 R"], "move_count": 10, "difficulty": 2, "description": "Knight move shape"}, {"id": "oll_17", "name": "Dot Case 17", "algorithm": "(R U R' U) (R' F R F') U2 (R' F R F')", "alternative_algorithms": ["F R' F' R U S' R U' R' S"], "move_count": 13, "difficulty": 2, "description": "No edges oriented - dot case"}, {"id": "oll_18", "name": "Dot Case 18", "algorithm": "(R U2 R') (R' F R F') U2 M' (U R U' r')", "alternative_algorithms": ["r U R' U R U2 r2 U' R U' R' U2 r"], "move_count": 14, "difficulty": 3, "description": "No edges oriented - dot case"}, {"id": "oll_19", "name": "Dot Case 19", "algorithm": "M U (R U R' U') M' (R' F R F')", "alternative_algorithms": ["r' R U R U R' U' r R2 F R F'"], "move_count": 11, "difficulty": 2, "description": "No edges oriented - dot case"}, {"id": "oll_20", "name": "Dot Case 20", "algorithm": "(r U R' U') M2 (U R U' R') U' M", "alternative_algorithms": ["M' U2 M U2 M' U M U2 M' U2 M"], "move_count": 11, "difficulty": 2, "description": "No edges oriented - dot case"}, {"id": "oll_25", "name": "OCLL 25", "algorithm": "(F' r U R') (U' r' F R)", "alternative_algorithms": ["R U2 R D R' U2 R D' R2"], "move_count": 9, "difficulty": 2, "description": "All corners oriented"}, {"id": "oll_26", "name": "OCLL 26", "algorithm": "R <PERSON> (R' U' R U') R'", "alternative_algorithms": ["R' U' R U' R' U2 R"], "move_count": 8, "difficulty": 2, "description": "All corners oriented"}, {"id": "oll_27", "name": "OCLL 27", "algorithm": "(R U R' U) (R U2 R')", "alternative_algorithms": ["L U L' U L U2 L'"], "move_count": 7, "difficulty": 1, "description": "All corners oriented"}, {"id": "oll_28", "name": "All Corners Oriented 28", "algorithm": "(r U R' U') M (U R U' R')", "alternative_algorithms": ["R' F R S R' F' R S'"], "move_count": 9, "difficulty": 1, "description": "All corners oriented"}, {"id": "oll_29", "name": "Awkward <PERSON> 29", "algorithm": "(R U R') U' (R U' R') (F' U' F) (R U R')", "alternative_algorithms": ["r2 D' r U r' D r2 U' r' U' r"], "move_count": 14, "difficulty": 3, "description": "Awkward shape pattern"}, {"id": "oll_30", "name": "Awkward Shape 30", "algorithm": "F U (R U2 R') U' (R U2 R') U' F'", "alternative_algorithms": ["F R' F R2 U' R' U' R U R' F2"], "move_count": 12, "difficulty": 2, "description": "Awkward shape pattern"}, {"id": "oll_31", "name": "P Shapes 31", "algorithm": "(R' U' F) (U R U' R') F' R", "alternative_algorithms": ["F R' F' R U R U R' U' R U' R'"], "move_count": 9, "difficulty": 1, "description": "P shape pattern"}, {"id": "oll_32", "name": "P Shapes 32", "algorithm": "S (R U R' U') (R' F R f')", "alternative_algorithms": ["L U F' U' L' U L F L'"], "move_count": 9, "difficulty": 2, "description": "P shape pattern"}, {"id": "oll_34", "name": "C Shape 34", "algorithm": "(r U r') (R U R' U') (r U' r')", "alternative_algorithms": ["f R f' U' r' U' R U M'"], "move_count": 10, "difficulty": 2, "description": "C shape pattern"}, {"id": "oll_35", "name": "Fish Shape 35", "algorithm": "(R U2 R') (R' F R F') (R U2 R')", "alternative_algorithms": ["f R U R' U' f' R U R' U R U2 R'"], "move_count": 10, "difficulty": 2, "description": "Fish shape pattern"}, {"id": "oll_36", "name": "<PERSON> <PERSON>hape 36", "algorithm": "(L' U' L U') (L' U L U) (L F' L' F)", "alternative_algorithms": ["L' U' L U' L' U L U L F' L' F"], "move_count": 13, "difficulty": 2, "description": "W shape pattern"}, {"id": "oll_37", "name": "Fish Shape 37", "algorithm": "F R (U' R' U') (R U R') F'", "alternative_algorithms": ["F R U' R' U' R U R' F'"], "move_count": 9, "difficulty": 1, "description": "Fish shape pattern"}, {"id": "oll_38", "name": "<PERSON> <PERSON><PERSON><PERSON> 38", "algorithm": "(R U R' U) (R U' R' U') (R' F R F')", "alternative_algorithms": ["L' U2 l' D' l U2 l' D l L"], "move_count": 12, "difficulty": 2, "description": "W shape pattern"}, {"id": "oll_39", "name": "Lightning Shape 39", "algorithm": "L F' (L' U' L U) F U' L'", "alternative_algorithms": ["f' L F L' U' L' U L S"], "move_count": 10, "difficulty": 2, "description": "Lightning bolt shape"}, {"id": "oll_40", "name": "Lightning Shape 40", "algorithm": "R' F (R U R' U') F' U R", "alternative_algorithms": ["f R' F' R U R U' R' S'"], "move_count": 10, "difficulty": 2, "description": "Lightning bolt shape"}, {"id": "oll_41", "name": "Awkward <PERSON> 41", "algorithm": "(R U R' U) (R U2 R') F (R U R' U') F'", "alternative_algorithms": ["F U R2 D R' U' R D' R2 F'"], "move_count": 14, "difficulty": 3, "description": "Awkward shape pattern"}, {"id": "oll_42", "name": "Awkward <PERSON> 42", "algorithm": "(R' U' R U') (R' U2 R) F (R U R' U') F'", "alternative_algorithms": ["F R' F' R U2 R' U' R2 U' R2 U2 R"], "move_count": 13, "difficulty": 2, "description": "Awkward shape pattern"}, {"id": "oll_43", "name": "<PERSON> 43", "algorithm": "R' U' (F' U F) R", "alternative_algorithms": ["F' U' L' U L F"], "move_count": 7, "difficulty": 2, "description": "P shape pattern"}, {"id": "oll_44", "name": "<PERSON> 44", "algorithm": "f (R U R' U') f'", "alternative_algorithms": ["F U R U' R' F'"], "move_count": 6, "difficulty": 1, "description": "P shape pattern"}, {"id": "oll_46", "name": "C Shape 46", "algorithm": "R' U' (R' F R F') U R", "alternative_algorithms": ["R' F' U' F R U' R' U2 R"], "move_count": 8, "difficulty": 1, "description": "C shape pattern"}, {"id": "oll_47", "name": "L Shapes 47", "algorithm": "F' (L' U' L U) (L' U' L U) F", "alternative_algorithms": ["F R' F' R U2 R U' R' U R U2 R'"], "move_count": 10, "difficulty": 2, "description": "L shape pattern"}, {"id": "oll_48", "name": "L Shapes 48", "algorithm": "F (R U R' U') (R U R' U') F'", "alternative_algorithms": ["F R' F' U2 R U R' U R2 U2 R'"], "move_count": 10, "difficulty": 2, "description": "L shape pattern"}, {"id": "oll_49", "name": "L Shapes 49", "algorithm": "r U' (r2 U) (r2 U) (r2) U' r", "alternative_algorithms": ["l U' l2 U l2 U l2 U' l"], "move_count": 10, "difficulty": 2, "description": "L shape pattern"}, {"id": "oll_50", "name": "L Shapes 50", "algorithm": "r' U (r2 U') (r2 U') (r2) U r'", "alternative_algorithms": ["l' U l2 U' l2 U' l2 U l'"], "move_count": 9, "difficulty": 1, "description": "L shape pattern"}, {"id": "oll_51", "name": "Line Shapes 51", "algorithm": "f (R U R' U') (R U R' U') f'", "alternative_algorithms": ["F' U' L' U L U' L' U L F"], "move_count": 10, "difficulty": 2, "description": "Line shape pattern"}, {"id": "oll_52", "name": "Line Shapes 52", "algorithm": "R' (F' U' F U') (R U R' U) R", "alternative_algorithms": ["R U R' U R U' B U' B' R'"], "move_count": 11, "difficulty": 2, "description": "Line shape pattern"}, {"id": "oll_53", "name": "L Shapes 53", "algorithm": "(r' U' R U') (R' U R U') (R' U2 r)", "alternative_algorithms": ["l' U' L U' L' U L U' L' U2 l"], "move_count": 11, "difficulty": 2, "description": "L shape pattern"}, {"id": "oll_54", "name": "L Shapes 54", "algorithm": "(r U R' U) (R U' R' U) (R U2 r')", "alternative_algorithms": ["l U L' U L U' L' U L U2 l'"], "move_count": 11, "difficulty": 2, "description": "L shape pattern"}, {"id": "oll_55", "name": "Line Shapes 55", "algorithm": "R U2 R2 (U' R U' R') U2 (F R F')", "alternative_algorithms": ["R' F U R U' R2 F' R2 U R' U' R"], "move_count": 11, "difficulty": 2, "description": "Line shape pattern"}, {"id": "oll_56", "name": "Line Shapes 56", "algorithm": "(r U r') (U R U' R') (U R U' R') (r U' r')", "alternative_algorithms": ["F R U R' U' R F' r U R' U' r'"], "move_count": 14, "difficulty": 2, "description": "Line shape pattern"}, {"id": "oll_57", "name": "All Corners Oriented 57", "algorithm": "(R U R' U') M' (U R U' r')", "alternative_algorithms": ["R U' R' S' R U R' S"], "move_count": 9, "difficulty": 2, "description": "All corners oriented"}]}, "PLL": {"name": "Permutation of Last Layer", "description": "Algorithms for permuting all pieces on the last layer", "cases": [{"id": "pll_aa", "name": "Aa Perm", "algorithm": "x (R' U R') D2 (R U' R') D2 R2 x'", "alternative_algorithms": [], "move_count": 11, "difficulty": 3, "description": "Clockwise corner 3-cycle"}, {"id": "pll_ab", "name": "<PERSON><PERSON>", "algorithm": "x R2 D2 (R U R') D2 (R U' R) x'", "alternative_algorithms": [], "move_count": 11, "difficulty": 3, "description": "Counter-clockwise corner 3-cycle"}, {"id": "pll_e", "name": "E Perm", "algorithm": "x' (R U' R' D) (R U R' D') (R U R' D) (R U' R' D') x", "alternative_algorithms": [], "move_count": 19, "difficulty": 4, "description": "Diagonal corner swap + diagonal edge swap"}, {"id": "pll_f", "name": "F Perm", "algorithm": "(R' U' F') (R U R' U') R' F R2 (U' R' U') (R U R' U) R", "alternative_algorithms": [], "move_count": 19, "difficulty": 3, "description": "Adjacent corner swap + adjacent edge swap"}, {"id": "pll_ga", "name": "Ga Perm", "algorithm": "R2 (U R' U R' U' R U') R2 D (U' R' U R) D'", "alternative_algorithms": ["R U R' F' R U R' U' R' F R U' R' F R2 U' R' U' R U R' F'"], "move_count": 15, "difficulty": 2, "description": "Adjacent corner swap + diagonal edge swap"}, {"id": "pll_gb", "name": "Gb Perm", "algorithm": "(R' U' R U) D' R2 (U R' U R U' R U') R2 D", "alternative_algorithms": [], "move_count": 15, "difficulty": 2, "description": "Adjacent corner swap + diagonal edge swap"}, {"id": "pll_gc", "name": "Gc Perm", "algorithm": "R2 (U' R U' R U R' U) R2 D' (U R U' R') D", "alternative_algorithms": ["R2 F2 R U2 R U2 R' F R U R' U' R' F R2"], "move_count": 15, "difficulty": 2, "description": "Adjacent corner swap + diagonal edge swap"}, {"id": "pll_gd", "name": "Gd Perm", "algorithm": "(R U R' U') D R2 (U' R U' R' U R' U) R2 D'", "alternative_algorithms": [], "move_count": 15, "difficulty": 2, "description": "Adjacent corner swap + diagonal edge swap"}, {"id": "pll_h", "name": "H Perm", "algorithm": "(M2 U' M2) U2 (M2 U' M2)", "alternative_algorithms": [], "move_count": 7, "difficulty": 1, "description": "Opposite edge swap"}, {"id": "pll_ja", "name": "<PERSON><PERSON>", "algorithm": "(R' U L') U2 (R U' R') U2 R L", "alternative_algorithms": ["R' U L' U2 R U' R' U2 R L"], "move_count": 11, "difficulty": 2, "description": "Adjacent corner swap + adjacent edge swap"}, {"id": "pll_jb", "name": "<PERSON><PERSON>", "algorithm": "(R U R' F') (R U R' U') R' F R2 U' R'", "alternative_algorithms": [], "move_count": 13, "difficulty": 2, "description": "Adjacent corner swap + adjacent edge swap"}, {"id": "pll_na", "name": "Na Perm", "algorithm": "(R U R' U) (R U R' F') (R U R' U') R' F R2 U' R' U2 (R U' R')", "alternative_algorithms": [], "move_count": 21, "difficulty": 3, "description": "Diagonal corner swap + opposite edge swap"}, {"id": "pll_nb", "name": "Nb Perm", "algorithm": "(R' U R U' R') (F' U' F) (R U R') (F R' F') (R U' R)", "alternative_algorithms": ["r' D' F r U' r' F' D r2 U r' U' r' F r F'"], "move_count": 17, "difficulty": 3, "description": "Diagonal corner swap + opposite edge swap"}, {"id": "pll_ra", "name": "Ra <PERSON>", "algorithm": "(R U' R' U') (R U R D) (R' U' R D') (R' U2 R')", "alternative_algorithms": [], "move_count": 16, "difficulty": 3, "description": "Adjacent corner swap + adjacent edge swap"}, {"id": "pll_rb", "name": "Rb <PERSON>", "algorithm": "(R' <PERSON>) (R U2) (R' F R) (U R' U' R') F' R2", "alternative_algorithms": ["R2 F R U R U' R' F' R U2 R' U2 R"], "move_count": 13, "difficulty": 2, "description": "Adjacent corner swap + adjacent edge swap"}, {"id": "pll_t", "name": "T Perm", "algorithm": "(R U R' U') (R' F R2) (U' R' U') (R U R' F')", "alternative_algorithms": [], "move_count": 14, "difficulty": 2, "description": "Adjacent corner swap + opposite edge swap"}, {"id": "pll_ua", "name": "Ua Perm", "algorithm": "(M2 U M) U2 (M' U M2)", "alternative_algorithms": ["R U R' U R' U' R2 U' R' U R' U R"], "move_count": 8, "difficulty": 2, "description": "Clockwise edge 3-cycle"}, {"id": "pll_ub", "name": "Ub Perm", "algorithm": "(M2 U' M) U2 (M' U' M2)", "alternative_algorithms": ["R2' U R U R' U' R3 U' R' U R'"], "move_count": 8, "difficulty": 2, "description": "Counter-clockwise edge 3-cycle"}, {"id": "pll_v", "name": "V Perm", "algorithm": "(R' U R' U') (R D' R' D) (R' U D') (R2 U' R2) D R2", "alternative_algorithms": ["R' U R U' x' U R U2 R' U' R U' R' U2 R U R' U'"], "move_count": 16, "difficulty": 3, "description": "Diagonal corner swap + adjacent edge swap"}, {"id": "pll_y", "name": "Y Perm", "algorithm": "F R (U' R' U') (R U R' F') (R U R' U') (R' F R F')", "alternative_algorithms": [], "move_count": 17, "difficulty": 3, "description": "Diagonal corner swap + adjacent edge swap"}, {"id": "pll_z", "name": "Z Perm", "algorithm": "(M2 U) (M2 U) (M' U2) M2 (U2 M')", "alternative_algorithms": [], "move_count": 9, "difficulty": 2, "description": "Adjacent edge swap"}]}}}}}}