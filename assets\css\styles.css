/* Base styles */

/* Hidden class */
.hidden {
  display: none;
}

/* Screen reader only class - visually hidden but accessible to screen readers */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
:root {
  /* Light mode colors */
  --bg-color: #f5f5f5;
  --text-color: #333;
  --container-bg: white;
  --header-bg: #a57865;
  --header-text: white;
  --btn-bg: #8a6553;
  --btn-hover: #755648;
  --timer-color: #a57865;
  --card-bg: #f9f9f9;
  --border-color: #eee;
  --shadow-color: rgba(0, 0, 0, 0.1);
  --ready-color: #4caf50;
  --running-color: #e74c3c;
  --inspection-color: #f39c12;
  --hover-bg: white;
  --accent-color: #a57865;
  --accent-color-hover: #8a6553;
}

/* RTL Support */
[dir="rtl"] {
  text-align: right;
  font-family: var(--font-rtl) !important;
}

/* Apply Rabar font to all text elements in RTL mode */
[dir="rtl"] body,
[dir="rtl"] .header-title,
[dir="rtl"] .header-center,
[dir="rtl"] .event-option,
[dir="rtl"] .settings-title,
[dir="rtl"] .settings-section-title,
[dir="rtl"] .checkbox-label,
[dir="rtl"] .stats-title,
[dir="rtl"] .stat-label,
[dir="rtl"] .times-panel-title,
[dir="rtl"] .solve-details-title,
[dir="rtl"] .solve-details-section-title,
[dir="rtl"] .radio-label,
[dir="rtl"] button,
[dir="rtl"] .action-button,
[dir="rtl"] #new-scramble,
[dir="rtl"] #settings-save,
[dir="rtl"] #solve-details-save,
[dir="rtl"] #clear-times,
[dir="rtl"] label,
[dir="rtl"] span:not(.cubing-icon):not(.fas):not(.far):not(.fal):not(.fab),
[dir="rtl"] .fmc-start-prompt {
  font-family: var(--font-rtl) !important;
}

/* Keep timer font as monospace for all languages */
.timer {
  font-family: monospace !important;
}

[dir="rtl"] .header-left {
  justify-self: end;
}

[dir="rtl"] .header-right {
  justify-self: start;
}

[dir="rtl"] .cubing-icon {
  margin-right: 0;
  margin-left: 10px;
}

/* RTL: Flip new scramble button icon 180 degrees */
[dir="rtl"] #new-scramble .fa-random {
  transform: rotate(180deg);
}

[dir="rtl"] .times-panel {
  left: auto;
  right: -350px;
  transition: right 0.3s ease, background-color 0.3s ease, box-shadow 0.3s ease;
}

[dir="rtl"] .times-panel.show {
  left: auto;
  right: 0;
}

/* RTL button positioning */
[dir="rtl"] .times-toggle {
  right: auto;
  left: 15px;
}

/* In LTR, settings button is on the right */
.header-right {
  justify-self: end;
}

.header-left {
  justify-self: start;
}

/* In RTL, settings button is on the left */
[dir="rtl"] header {
  grid-template-columns: 1fr auto 1fr;
  direction: rtl;
}

/* Swap header elements for RTL */
[dir="rtl"] .header-right {
  grid-column: 1;
  justify-self: start;
}

[dir="rtl"] .header-center {
  grid-column: 2;
  justify-self: center;
}

[dir="rtl"] .header-left {
  grid-column: 3;
  justify-self: end;
}

[dir="rtl"] .container {
  transition: margin-right 0.3s ease, width 0.3s ease, transform 0.3s ease;
}

[dir="rtl"] .times-panel-close {
  margin-right: 0;
  margin-left: 20px;
}

[dir="rtl"] .times-panel-header {
  padding-left: 0;
  padding-right: 25px;
}

[dir="rtl"] body.panel-open .container {
  margin-left: 0;
  margin-right: 350px;
}

/* Dropdown selectors (language, timer mode, decimal places, microphone, and inspection sound) */
.language-selector,
.timer-mode-selector,
.decimal-places-selector,
.microphone-selector,
.inspection-sound-selector {
  width: 100%;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid var(--border-color);
  background-color: var(--container-bg);
  color: var(--text-color);
  font-size: 0.9rem;
  transition: border-color 0.3s ease, background-color 0.3s ease;
}

.language-selector:focus,
.timer-mode-selector:focus,
.decimal-places-selector:focus,
.inspection-sound-selector:focus {
  outline: none;
  border-color: var(--header-bg);
}

/* Disabled state for decimal places and inspection sound selectors */
.decimal-places-selector:disabled,
.inspection-sound-selector:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: var(--card-bg);
  color: var(--text-color-muted);
}

.settings-option.disabled {
  opacity: 0.6;
}

.settings-option.disabled .dropdown-label span {
  color: var(--text-color-muted);
}

/* Dropdown label */
.dropdown-label {
  display: flex;
  flex-direction: column;
  gap: 5px;
  width: 100%;
}

/* Disabled option styling */
select option:disabled {
  color: #888;
  font-style: italic;
  background-color: #f0f0f0;
}

.dark-mode select option:disabled {
  background-color: #444;
  color: #aaa;
}

/* Dark mode colors */
.dark-mode {
  --bg-color: #121212;
  --text-color: #f0f0f0;
  --container-bg: #1e1e1e;
  --header-bg: #6d4c41;
  --header-text: #ffffff;
  --btn-bg: #5d4037;
  --btn-hover: #4e342e;
  --timer-color: #c69c84;
  --card-bg: #2d2d2d;
  --border-color: #444;
  --shadow-color: rgba(0, 0, 0, 0.3);
  --ready-color: #66bb6a;
  --running-color: #ef5350;
  --inspection-color: #ffb74d;
  --hover-bg: #383838;
  --accent-color: #c69c84;
  --accent-color-hover: #a57865;
}

body {
  font-family: var(--font-primary);
  margin: 0;
  padding: 0;
  background-color: var(--bg-color);
  color: var(--text-color);
  height: 100vh;
  overflow: hidden;
  transition: background-color 0.3s ease, color 0.3s ease;
  /* Disable pull-to-refresh on mobile devices to prevent conflicts with swipe gestures */
  overscroll-behavior: none;
  overscroll-behavior-y: none;
  -webkit-overflow-scrolling: touch;
}

.container {
  display: grid;
  grid-template-rows: auto 1fr auto;
  height: 100vh;
  width: 100%;
  background-color: var(--bg-color);
  overflow: hidden;
  transition: background-color 0.3s ease;
  /* Additional overscroll prevention for container */
  overscroll-behavior: none;
  overscroll-behavior-y: none;
}

/* Header styles */
header {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 52.8px;
  background-color: transparent;
  color: var(--header-text);
  box-shadow: none;
  z-index: 10;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.header-left {
  position: absolute;
  left: 15px;
  top: 10px;
}

.header-center {
  display: flex;
  align-items: center;
  gap: 10px;
  justify-content: center;
}

.header-right {
  position: absolute;
  right: 15px;
  top: 10px;
}

/* RTL header adjustments */
[dir="rtl"] .header-right {
  right: auto;
  left: 15px;
}

[dir="rtl"] .header-left {
  left: auto;
  right: 15px;
}

h1 {
  color: white;
  margin: 0;
  font-size: 1.2rem;
}

.event-selector {
  position: relative;
  display: inline-block;
  min-width: 150px;
}

.event-selector-btn {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: transparent;
  color: var(--header-text);
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  width: 100%;
  text-align: left;
  transition: background-color 0.3s ease;
}

.event-selector-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Light mode support for event selector */
body:not(.dark-mode) .event-selector-btn {
  color: var(--text-color);
}

body:not(.dark-mode) .event-selector-btn:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.event-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  background-color: var(--container-bg);
  border: none;
  border-radius: 4px;
  box-shadow: 0 4px 12px var(--shadow-color);
  z-index: 100;
  max-height: 300px;
  overflow-y: auto;
  display: none;
  margin-top: 5px;
  transition: background-color 0.3s ease;
}

.event-dropdown.show {
  display: block;
}

.event-option {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  color: var(--text-color);
  transition: background-color 0.3s ease;
}

.event-option:hover {
  background-color: var(--card-bg);
}

/* Event divider */
.event-divider {
  height: 1px;
  background-color: var(--border-color);
  margin: 8px 0;
}

.sessions-divider {
  margin-top: 12px;
  margin-bottom: 12px;
  background-color: var(--accent-color);
  opacity: 0.5;
}

/* New Session option */
.new-session-option {
  font-weight: bold;
  color: var(--accent-color);
}

.new-session-option:hover {
  background-color: rgba(165, 120, 101, 0.1);
}

.new-session-option .fas {
  color: var(--accent-color);
  margin-right: 10px;
}

/* Cubing icons styles */
.cubing-icon {
  vertical-align: middle;
  margin-right: 10px;
}

/* Main content layout */
.main-content {
  display: grid;
  grid-template-areas:
    "scramble scramble"
    "timer timer"
    "stats visualization";
  grid-template-rows: auto 1fr auto;
  grid-template-columns: 1fr 1fr;
  height: calc(100% - 20px); /* Leave space at bottom */
  padding: 10px;
  gap: 15px;
  position: relative;
  margin-bottom: 20px; /* Add space at bottom */
}

/* Scramble section */
.scramble-section {
  grid-area: scramble;
  text-align: center;
  padding: 10px;
}

/* Lottie Loader Styles */
.lottie-loader {
  display: inline-block;
  margin: 0 auto;
  vertical-align: middle;
}

/* Scramble loader - now uses Lottie */
.scramble-loader {
  display: inline-block;
  width: 50px;
  height: 50px;
  margin: 0 auto;
  vertical-align: middle;
}

/* Fallback for when Lottie fails to load */
.scramble-loader.fallback {
  border: 3px solid rgba(165, 120, 101, 0.3);
  border-radius: 50%;
  border-top-color: #a57865;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Dark mode fallback loader */
.dark-mode .scramble-loader.fallback {
  border: 3px solid rgba(165, 120, 101, 0.2);
  border-top-color: #a57865;
}

.scramble {
  font-family: monospace;
  font-size: calc(
    1.6rem * var(--font-scale)
  ); /* Base font size with scale factor */
  padding: 12px;
  background-color: var(--card-bg);
  border-radius: 8px;
  min-height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  direction: ltr;
  word-wrap: break-word;
  line-height: 1.7; /* Increased line height */
  letter-spacing: 0.1em; /* Added letter spacing */
  box-shadow: 0 2px 5px var(--shadow-color);
  color: var(--text-color);
  transition: background-color 0.3s ease, color 0.3s ease, box-shadow 0.3s ease;
}

/* Puzzle-specific font sizes */
.scramble.event-222 {
  font-size: calc(2.1rem * var(--font-scale)); /* 2x2 has short scrambles */
}

.scramble.event-333,
.scramble.event-333oh,
.scramble.event-333fm,
.scramble.event-333bf {
  font-size: calc(1.9rem * var(--font-scale)); /* 3x3 and variants */
}

.scramble.event-444,
.scramble.event-444bf {
  font-size: calc(1.8rem * var(--font-scale)); /* 4x4 */
}

.scramble.event-555,
.scramble.event-555bf {
  font-size: calc(1.7rem * var(--font-scale)); /* 5x5 */
}

.scramble.event-666,
.scramble.event-777 {
  font-size: calc(
    1.4rem * var(--font-scale)
  ); /* 6x6 and 7x7 have long scrambles */
  letter-spacing: 0.08em; /* Slightly reduced letter spacing for long scrambles */
}

.scramble.event-skewb,
.scramble.event-pyram {
  font-size: calc(
    2rem * var(--font-scale)
  ); /* Skewb and Pyraminx have short scrambles */
}

.scramble.event-sq1 {
  font-size: calc(1.8rem * var(--font-scale)); /* Square-1 */
}

.scramble.event-clock {
  font-size: calc(1.9rem * var(--font-scale)); /* Clock */
}

.scramble.event-minx {
  font-size: calc(
    1.5rem * var(--font-scale)
  ); /* Megaminx has very long scrambles */
}

/* Timer */
.timer-container {
  grid-area: timer;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px; /* Add more space below timer */
  position: relative;
}

/* Hide UI elements during timing and inspection */
body.timing-active header,
body.timing-active .scramble-section,
body.timing-active .stats-container,
body.timing-active .visualization,
body.timing-active .times-panel,
body.timing-active .times-toggle,
body.timing-active .stackmat-status,
body.inspection-active header,
body.inspection-active .scramble-section,
body.inspection-active .stats-container,
body.inspection-active .visualization,
body.inspection-active .times-panel,
body.inspection-active .stackmat-status {
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

/* Keep times toggle visible during inspection but hide during timing */
body.inspection-active .times-toggle {
  opacity: 1;
  visibility: visible;
}

/* Center timer during timing and inspection */
body.timing-active .timer-container,
body.inspection-active .timer-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 100;
}

/* Stackmat status styles */
.stackmat-status {
  display: none;
  position: absolute;
  top: 10px;
  right: 10px;
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 12px;
  font-size: 0.9rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 5;
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.stackmat-status.show {
  display: block;
}

.stackmat-connection {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.stackmat-connection i {
  font-size: 1.1rem;
}

.stackmat-connection.connected {
  color: #28a745;
}

.stackmat-connection.disconnected {
  color: #dc3545;
}

.stackmat-timer-state {
  font-weight: 500;
  text-align: center;
  color: var(--text-color);
}

.stackmat-timer-state.ready {
  color: #ffc107;
}

.stackmat-timer-state.running {
  color: #28a745;
}

.stackmat-timer-state.stopped {
  color: #17a2b8;
}

/* Mobile responsive Stackmat status */
@media (max-width: 768px) {
  .stackmat-status {
    top: 5px;
    right: 5px;
    padding: 8px;
    font-size: 0.8rem;
  }

  .stackmat-connection {
    gap: 6px;
    margin-bottom: 6px;
  }

  .stackmat-connection i {
    font-size: 1rem;
  }
}

/* Manual Input Timer */
.manual-input-container {
  display: none; /* Hidden by default */
  width: 100%;
  max-width: 400px;
  margin-top: 20px;
}

.manual-input-wrapper {
  position: relative;
  width: 100%;
  display: flex;
  justify-content: center;
  /* Ensure the wrapper doesn't interfere with absolute positioning */
  isolation: isolate;
}

.manual-time-input {
  width: 100%;
  max-width: 300px; /* Limit width for better centering */
  padding: 20px 60px 20px 20px; /* Add extra padding on the right for the inspection timer */
  font-size: 2rem;
  font-family: var(--font-timer);
  text-align: center;
  border: 2px solid var(--border-color);
  border-radius: 8px;
  background-color: var(--container-bg);
  color: var(--text-color);
  transition: border-color 0.3s ease, background-color 0.3s ease,
    color 0.3s ease;
  box-sizing: border-box; /* Include padding in width calculation */
  position: relative; /* Ensure proper stacking context */
}

.manual-time-input:focus {
  outline: none;
  border-color: #a57865;
}

.input-container {
  position: relative;
  width: 100%;
  max-width: 300px;
  display: inline-block;
}

.manual-inspection {
  position: absolute;
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
  font-size: 1.5rem;
  font-family: var(--font-timer);
  color: var(--text-color);
  opacity: 0.4;
  transition: color 0.3s ease, opacity 0.3s ease;
  pointer-events: none; /* Allow clicks to pass through to the input */
  z-index: 2; /* Higher z-index to ensure it appears above the input */
  margin: 0; /* Ensure no margin affects positioning */
  padding: 0; /* Ensure no padding affects positioning */
  width: auto; /* Don't take up extra width */
}

/* Red color for inspection time when it's running out */
.manual-inspection.warning {
  color: #ff5252;
  opacity: 0.6;
}

/* Touch indicator removed */

.timer {
  font-size: 6rem;
  font-family: monospace;
  -webkit-user-select: none;
  user-select: none;
  font-weight: bold;
  color: var(--timer-color);
  transition: color 0.2s;
  text-align: center;
  cursor: pointer; /* Show pointer cursor */
  touch-action: manipulation; /* Optimize for touch */
  -webkit-tap-highlight-color: transparent; /* Remove tap highlight on mobile */
}

/* Visualization */
.visualization {
  grid-area: visualization;
  display: flex;
  justify-content: center;
  align-items: center;
  height: auto;
  background-color: var(--card-bg);
  border-radius: 8px;
  padding: 10px;
  box-shadow: 0 2px 5px var(--shadow-color);
  overflow: hidden; /* Keep content inside the box */
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

/* Make sure twisty player stays within bounds */
.visualization > * {
  max-width: 100%;
  max-height: 100%;
}

/* MBLD Visualization */
.visualization.clickable {
  cursor: pointer;
}

.mbld-visualization-container {
  width: 100%;
  height: 180px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: var(--bg-color-secondary);
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
  border: 2px dashed var(--accent-color);
  position: relative;
  z-index: 100; /* Ensure it's above other elements */
  -webkit-tap-highlight-color: rgba(
    0,
    0,
    0,
    0
  ); /* Remove tap highlight on mobile */
  touch-action: manipulation; /* Improve touch handling */
}

.mbld-visualization-container:hover {
  background-color: var(--accent-color);
  transform: scale(1.02);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.mbld-visualization-container:active {
  transform: scale(0.98);
  background-color: var(--accent-color);
}

/* No tap icon needed */

.mbld-visualization-text {
  font-size: 1rem;
  font-weight: bold;
  color: var(--text-color);
  text-align: center;
  padding: 10px;
}

/* Set minimum heights for containers */
.stats-container {
  min-height: 180px;
}

.visualization {
  min-height: 180px;
}

.instructions {
  margin: 10px 0;
  color: #7f8c8d;
  font-size: 0.85rem;
  text-align: center;
}

/* Buttons */
button {
  padding: 8px 16px;
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

button:hover {
  background-color: #2980b9;
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Header action buttons (like next scramble button) */
header .action-button {
  background-color: transparent;
  color: var(--header-text);
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 0.85rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

header .action-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Light mode support for header action buttons */
body:not(.dark-mode) header .action-button {
  color: var(--text-color);
}

body:not(.dark-mode) header .action-button:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

/* Share button - minimal, integrated style */
#stat-detail-share {
  background-color: transparent;
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  padding: 6px 12px;
  font-size: 0.85rem;
  border-radius: 6px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

#stat-detail-share:hover {
  background-color: var(--card-bg);
  color: var(--text-color);
  border-color: var(--accent-color);
  transform: none;
  box-shadow: none;
}

#stat-detail-share i {
  font-size: 0.9rem;
}

/* Settings button */
.settings-button {
  background-color: transparent;
  color: white;
  padding: 8px;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  border: none;
  cursor: pointer;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.settings-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Light mode support for header buttons */
body:not(.dark-mode) .settings-button,
body:not(.dark-mode) .learning-button {
  color: var(--text-color);
}

body:not(.dark-mode) .settings-button:hover,
body:not(.dark-mode) .learning-button:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

/* Dark mode support for both buttons */
.dark-mode .settings-button,
.dark-mode .learning-button {
  color: var(--text-color);
}

.dark-mode .settings-button:hover,
.dark-mode .learning-button:hover {
  background-color: var(--hover-bg);
}

#solve-details-save {
  margin: 0 auto;
}

/* Timer states */
.ready {
  color: var(--ready-color) !important;
}

.running {
  color: var(--running-color) !important;
}

.inspection {
  color: var(--inspection-color) !important;
  font-size: 6rem; /* Match timer font size */
}

/* When both inspection and ready classes are applied, ready takes precedence for color */
.inspection.ready {
  color: var(--ready-color) !important;
  font-size: 6rem; /* Match timer font size */
}

/* Stackmat timer running animation */
.timer.running {
  animation: stackmat-pulse 1.5s ease-in-out infinite;
}

@keyframes stackmat-pulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
}

/* Stats section */
.stats-container {
  grid-area: stats;
  background-color: var(--card-bg);
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 5px var(--shadow-color);
  display: flex;
  flex-direction: column;
  overflow-y: auto; /* Allow scrolling if needed */
  height: auto;
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;
}

.stats-container:hover {
  box-shadow: 0 4px 8px var(--shadow-color);
  transform: translateY(-1px);
}

/* Stats title removed */

.stats {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 8px;
}

/* MBLD and FMC Stats specific styling */
.mbld-stats-content,
.fmc-stats-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.mbld-stats-content .stats-row,
.fmc-stats-content .stats-row {
  padding: 6px 0;
  margin-bottom: 2px;
  border-bottom: 1px solid rgba(165, 120, 101, 0.2);
}

.mbld-stats-content .stats-label,
.fmc-stats-content .stats-label {
  font-size: 0.9rem;
}

.mbld-stats-content .stats-value,
.fmc-stats-content .stats-value {
  font-size: 0.95rem;
  font-weight: bold;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  padding: 5px 0;
  border-bottom: 1px solid var(--border-color);
  transition: border-color 0.3s ease;
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-label {
  font-size: 0.9rem;
  color: var(--text-color);
  opacity: 0.7;
  transition: color 0.3s ease;
}

.stat-value {
  font-weight: bold;
  font-family: monospace;
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

/* Statistics Details Panel */
.stats-details-panel {
  position: fixed;
  top: 100%;
  left: 0;
  width: 100%;
  height: 100vh; /* Full screen height */
  background-color: var(--container-bg);
  transition: top 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  z-index: 1600; /* Higher than times panel (z-index: 1500) */
  display: flex;
  flex-direction: column;
  box-shadow: 0 -4px 20px var(--shadow-color);
}

.stats-details-panel.show {
  top: 0; /* Full screen, hide header */
}

/* Stats Row Layout for bigger screens */
.stats-row {
  display: flex;
  gap: 2rem;
  margin-bottom: 2rem;
}

.stats-row .stats-section {
  flex: 1;
  min-width: 0; /* Allow sections to shrink */
}

.stats-row .stats-import-export-section {
  flex: 1;
  min-width: 0;
}

.stats-details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  border-bottom: 2px solid var(--border-color);
  background-color: var(--card-bg);
  min-height: 60px;
  gap: 15px;
}

.stats-details-close {
  background: none;
  border: none;
  color: var(--text-color);
  font-size: 1.2rem;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
}

.stats-details-close:hover {
  background-color: var(--hover-bg);
  color: var(--accent-color);
}

.stats-details-title-container {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.stats-details-title-text {
  font-size: 1.4rem;
  font-weight: bold;
  color: var(--text-color);
  transition: color 0.3s ease;
  white-space: nowrap;
}

.stats-event-selector {
  position: relative;
  display: inline-block;
}

.stats-event-selector-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: var(--container-bg);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 1rem;
  font-weight: 500;
  color: var(--text-color);
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 150px;
  max-width: 220px;
}

.stats-event-selector-btn:hover {
  border-color: var(--accent-color);
  background-color: var(--hover-bg);
}

.stats-event-selector-btn:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 2px rgba(165, 120, 101, 0.2);
}

.stats-event-text {
  flex: 1;
  text-align: left;
}

.stats-event-icon {
  margin-right: 10px;
  vertical-align: middle;
}

.stats-event-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  background-color: var(--container-bg);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  box-shadow: 0 4px 12px var(--shadow-color);
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
  display: none;
  margin-top: 5px;
}

.stats-event-dropdown.show {
  display: block;
}

.stats-event-option {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  color: var(--text-color);
  transition: background-color 0.3s ease;
}

.stats-event-option:hover {
  background-color: var(--hover-bg);
}

.stats-event-option .cubing-icon {
  margin-right: 10px;
  vertical-align: middle;
}

.stats-event-option span:not(.cubing-icon) {
  flex: 1;
}

.stats-event-divider {
  height: 1px;
  background-color: var(--border-color);
  margin: 8px 0;
}

.stats-sessions-divider {
  margin-top: 12px;
  margin-bottom: 12px;
  background-color: var(--accent-color);
  opacity: 0.5;
}

.stats-details-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px 25px;
  background-color: var(--bg-color);
}

/* Stats Sections */
.stats-section {
  margin-bottom: 30px;
  background-color: var(--container-bg);
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px var(--shadow-color);
  transition: all 0.3s ease;
}

.stats-section:hover {
  box-shadow: 0 4px 12px var(--shadow-color);
  transform: translateY(-2px);
}

.stats-section-title {
  font-size: 1.2rem;
  font-weight: bold;
  color: var(--accent-color);
  margin: 0 0 15px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid var(--accent-color);
  transition: color 0.3s ease;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
}

.stat-card {
  background-color: var(--card-bg);
  border-radius: 8px;
  padding: 15px;
  text-align: center;
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.stat-card:hover {
  border-color: var(--accent-color);
  box-shadow: 0 2px 8px var(--shadow-color);
  transform: translateY(-2px);
}

.stat-card-label {
  font-size: 0.9rem;
  color: var(--text-color);
  opacity: 0.8;
  margin-bottom: 8px;
  font-weight: 500;
}

.stat-card-value {
  font-size: 1.3rem;
  font-weight: bold;
  font-family: monospace;
  color: var(--accent-color);
  transition: color 0.3s ease;
}

/* Records List */
.records-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.record-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  background-color: var(--card-bg);
  border-radius: 8px;
  border-left: 4px solid var(--accent-color);
  transition: all 0.3s ease;
}

.record-item:hover {
  background-color: var(--hover-bg);
  transform: translateX(5px);
}

.record-label {
  font-size: 1rem;
  color: var(--text-color);
  font-weight: 500;
}

.record-value {
  font-size: 1.1rem;
  font-weight: bold;
  font-family: monospace;
  color: var(--accent-color);
}

/* Chart Container */
.chart-container {
  background-color: var(--card-bg);
  border-radius: 8px;
  padding: 20px;
  border: 1px solid var(--border-color);
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 250px;
}

.chart-container canvas {
  max-width: 100%;
  height: auto;
}

/* Analysis Grid */
.analysis-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.analysis-item {
  display: flex;
  flex-direction: column;
  padding: 15px;
  background-color: var(--card-bg);
  border-radius: 8px;
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.analysis-item:hover {
  border-color: var(--accent-color);
  box-shadow: 0 2px 8px var(--shadow-color);
}

.analysis-label {
  font-size: 0.9rem;
  color: var(--text-color);
  opacity: 0.8;
  margin-bottom: 8px;
  font-weight: 500;
}

.analysis-value {
  font-size: 1.2rem;
  font-weight: bold;
  font-family: monospace;
  color: var(--accent-color);
}

/* Predictions Grid */
.predictions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 15px;
}

.prediction-item {
  display: flex;
  flex-direction: column;
  padding: 15px;
  background: linear-gradient(135deg, var(--card-bg) 0%, var(--hover-bg) 100%);
  border-radius: 8px;
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.prediction-item:hover {
  border-color: var(--accent-color);
  box-shadow: 0 4px 12px var(--shadow-color);
  transform: translateY(-3px);
}

.prediction-label {
  font-size: 0.9rem;
  color: var(--text-color);
  opacity: 0.8;
  margin-bottom: 8px;
  font-weight: 500;
}

.prediction-value {
  font-size: 1.2rem;
  font-weight: bold;
  font-family: monospace;
  color: var(--accent-color);
}

/* Learning Button - Match settings button exactly */
.learning-button {
  background-color: transparent;
  color: white;
  padding: 8px;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  border: none;
  cursor: pointer;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.learning-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Learning Modal */
.learning-modal {
  position: fixed;
  top: -100%;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: var(--container-bg);
  transition: top 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  z-index: 2000;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 20px var(--shadow-color);
}

.learning-modal.show {
  top: 0;
}

.learning-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  border-bottom: 2px solid var(--border-color);
  background-color: var(--card-bg);
  min-height: 60px;
  gap: 15px;
}

.learning-modal-title-container {
  display: flex;
  align-items: center;
  gap: 20px;
  flex: 1;
}

.learning-modal-title-text {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-color);
}

.learning-selectors {
  display: flex;
  align-items: center;
  gap: 15px;
}

.learning-puzzle-selector,
.learning-method-selector {
  position: relative;
}

.learning-puzzle-selector-btn,
.learning-method-selector-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background-color: var(--hover-bg);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  color: var(--text-color);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  min-width: 120px;
  justify-content: space-between;
}

.learning-puzzle-selector-btn:hover,
.learning-method-selector-btn:hover {
  background-color: var(--accent-color);
  color: white;
}

.learning-puzzle-dropdown,
.learning-method-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  min-width: 120px;
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  box-shadow: 0 4px 12px var(--shadow-color);
  z-index: 1000;
  display: none;
  margin-top: 4px;
}

.learning-puzzle-dropdown.show,
.learning-method-dropdown.show {
  display: block;
}

.learning-puzzle-option,
.learning-method-option {
  padding: 10px 15px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  font-size: 0.9rem;
}

.learning-puzzle-option:hover,
.learning-method-option:hover {
  background-color: var(--hover-bg);
}

.learning-puzzle-option:first-child,
.learning-method-option:first-child {
  border-radius: 6px 6px 0 0;
}

.learning-puzzle-option:last-child,
.learning-method-option:last-child {
  border-radius: 0 0 6px 6px;
}

.learning-modal-close {
  background: none;
  border: none;
  color: var(--text-color);
  font-size: 1.2rem;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
}

.learning-modal-close:hover {
  background-color: var(--hover-bg);
  color: var(--accent-color);
}

.learning-modal-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px 25px;
}

.learning-tabs-container {
  margin-bottom: 20px;
}

.learning-tabs {
  display: flex;
  gap: 2px;
  background-color: var(--hover-bg);
  border-radius: 8px;
  padding: 4px;
}

.learning-tab {
  flex: 1;
  padding: 12px 20px;
  background: none;
  border: none;
  color: var(--text-color);
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.3s ease;
  font-weight: 500;
  font-size: 0.95rem;
}

.learning-tab:hover {
  background-color: var(--card-bg);
}

.learning-tab.active {
  background-color: var(--accent-color);
  color: white;
}

.learning-content-area {
  margin-top: 10px;
}

.learning-algset-info {
  margin-bottom: 25px;
  padding: 20px;
  background-color: var(--card-bg);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.learning-algset-info h3 {
  margin: 0 0 8px 0;
  color: var(--accent-color);
  font-size: 1.2rem;
  font-weight: 600;
}

.learning-algset-info p {
  margin: 0;
  color: var(--text-color);
  opacity: 0.8;
  font-size: 0.95rem;
  line-height: 1.4;
}

.learning-algorithms-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.algorithm-card {
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 20px;
  transition: all 0.3s ease;
}

.algorithm-card:hover {
  border-color: var(--accent-color);
  box-shadow: 0 4px 12px var(--shadow-color);
  transform: translateY(-2px);
}

.algorithm-card-header {
  margin-bottom: 15px;
}

.algorithm-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 5px 0;
}

.algorithm-description {
  font-size: 0.85rem;
  color: var(--text-color);
  opacity: 0.7;
  margin: 0;
  line-height: 1.3;
}

.algorithm-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* Slot tabs for F2L algorithms */
.algorithm-slot-tabs {
  display: flex;
  gap: 2px;
  background-color: var(--hover-bg);
  border-radius: 6px;
  padding: 2px;
  margin-bottom: 12px;
}

.algorithm-slot-tab {
  flex: 1;
  padding: 6px 8px;
  background: none;
  border: none;
  color: var(--text-color);
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.3s ease;
  font-size: 0.8rem;
  font-weight: 500;
  text-align: center;
}

.algorithm-slot-tab:hover {
  background-color: var(--card-bg);
}

.algorithm-slot-tab.active {
  background-color: var(--accent-color);
  color: white;
}

.algorithm-slot-content {
  display: none;
}

.algorithm-slot-content.active {
  display: block;
}

.algorithm-sequence {
  font-family: "Courier New", monospace;
  font-size: 1rem;
  font-weight: 600;
  color: var(--accent-color);
  background-color: var(--hover-bg);
  padding: 12px;
  border-radius: 6px;
  border: 1px solid var(--border-color);
  text-align: center;
  letter-spacing: 1px;
}

.algorithm-setup {
  font-family: "Courier New", monospace;
  font-size: 0.9rem;
  color: var(--text-color);
  opacity: 0.8;
  background-color: var(--container-bg);
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid var(--border-color);
  text-align: center;
}

.algorithm-setup::before {
  content: "Setup: ";
  font-weight: 600;
  color: var(--accent-color);
}

/* Algorithm Interaction Styles */
.main-algorithm,
.alternative-algorithm {
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 4px;
  padding: 8px 12px;
  margin: 4px 0;
  border: 1px solid transparent;
}

.main-algorithm:hover,
.alternative-algorithm:hover {
  background-color: var(--hover-bg);
  border-color: var(--accent-color);
  transform: translateX(2px);
}

.main-algorithm.active-algorithm,
.alternative-algorithm.active-algorithm {
  background-color: var(--accent-color);
  color: white;
  border-color: var(--accent-color);
}

.main-algorithm {
  font-weight: 600;
  border-color: var(--border-color);
}

.alternative-algorithm::before {
  content: "Alt: ";
  font-size: 0.8rem;
  opacity: 0.7;
  font-weight: 500;
}

/* Algorithm Header Layout */
.algorithm-header-content {
  display: flex;
  gap: 15px;
  align-items: flex-start;
}

.algorithm-header-text {
  flex: 1;
  min-width: 0; /* Prevent overflow */
}

.algorithm-3d-container.header-visualization {
  flex: 0 0 180px; /* Smaller fixed width for header visualization */
  height: 150px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: var(--bg-color);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  position: relative;
  overflow: hidden;
}

.algorithm-3d-container {
  display: flex;
  justify-content: center;
  align-items: center;
  background: var(--bg-color);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  position: relative;
  overflow: hidden;
}

.algorithm-3d-container canvas {
  width: 100%;
  height: 100%;
  border-radius: 4px;
  display: block;
  max-width: 100%;
  max-height: 100%;
}

/* Loading state for 3D visualizations */
.algorithm-3d-container:empty::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 40px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--accent-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.algorithm-3d-container:empty::after {
  content: "Loading 3D Visualization...";
  position: absolute;
  top: 60%;
  left: 50%;
  transform: translateX(-50%);
  font-size: 0.9rem;
  color: var(--text-color);
  opacity: 0.7;
}

@keyframes spin {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

/* Mobile responsive for learning modal */
@media (max-width: 768px) {
  .learning-modal-header {
    padding: 15px 20px;
    min-height: 50px;
  }

  .learning-modal-title-container {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }

  .learning-selectors {
    flex-direction: column;
    gap: 10px;
    width: 100%;
  }

  .learning-puzzle-selector-btn,
  .learning-method-selector-btn {
    width: 100%;
  }

  .learning-modal-content {
    padding: 15px 20px;
  }

  .learning-algorithms-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .algorithm-card {
    padding: 15px;
  }

  .learning-tabs {
    flex-direction: column;
    gap: 2px;
  }

  .learning-tab {
    padding: 10px 15px;
  }

  /* Mobile responsive for algorithm header */
  .algorithm-header-content {
    flex-direction: column;
    gap: 10px;
  }

  .algorithm-3d-container.header-visualization {
    flex: none;
    width: 100%;
    height: 120px; /* Smaller height on mobile */
  }
}

/* Times panel (slide-in from left) */
.times-panel {
  position: fixed;
  top: 0;
  left: -350px; /* Start off-screen on the left */
  width: 350px;
  height: 100vh;
  background-color: var(--card-bg);
  transition: left 0.3s ease, background-color 0.3s ease, box-shadow 0.3s ease;
  z-index: 1500;
  display: flex;
  flex-direction: column;
}

.times-panel.show {
  left: 0;
}

/* Prevent horizontal scrollbar */
body {
  overflow-x: hidden;
}

/* Smooth transition for container */
.container {
  transition: margin-left 0.3s ease, width 0.3s ease, transform 0.3s ease;
  width: 100%;
}

.times-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-left: 25px;
  background-color: transparent;
  color: var(--text-color);
  height: 52.8px;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.times-panel-title {
  font-size: 1.1rem;
  font-weight: bold;
}

.times-panel-close {
  background: transparent;
  color: white;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0;
  margin-right: 20px;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.times-panel-close:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.times-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.times-list ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.times-list li {
  padding: 12px 10px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.2s, border-color 0.3s ease;
  color: var(--text-color);
}

.times-list li:hover {
  background-color: var(--hover-bg);
  box-shadow: 0 1px 3px var(--shadow-color);
}

.times-list li:last-child {
  border-bottom: none;
}

/* Comment indicator styles moved to .time-value section */

.time-info {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.time-value {
  font-size: 1.1rem;
  font-weight: bold;
  font-family: monospace;
  display: flex;
  align-items: center;
}

.time-date {
  font-size: 0.8rem;
  color: var(--text-color);
  opacity: 0.7;
  margin-top: 3px;
  transition: color 0.3s ease;
}

.comment-indicator {
  color: #3498db;
  margin-left: 5px;
  font-size: 0.8rem;
  transition: color 0.3s ease;
}

/* RTL specific spacing for comment indicator */
[dir="rtl"] .comment-indicator {
  margin-left: 0;
  margin-right: 5px;
}

.dark-mode .comment-indicator {
  color: #64b5f6;
}

.delete-time {
  background-color: #e74c3c;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  padding: 5px 8px;
  font-size: 0.8rem;
  height: 28px;
  width: 28px;
}

.delete-time:hover {
  background-color: #c0392b;
}

.times-panel-footer {
  padding: 15px;
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: border-color 0.3s ease;
}

.footer-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

#clear-times-btn {
  background-color: #e74c3c;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  padding: 8px 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.3s ease;
}

#clear-times-btn:hover {
  background-color: #c0392b;
}

#edit-session-btn {
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  padding: 8px 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.3s ease;
}

#edit-session-btn:hover {
  background-color: #2980b9;
}

#edit-session-btn.hidden {
  display: none;
}

#stats-btn {
  background-color: #9b59b6;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  padding: 8px 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.3s ease;
}

#stats-btn:hover {
  background-color: #8e44ad;
}

#stats-btn i {
  font-size: 1rem;
}

.action-button {
  background-color: var(--accent-color, #a57865);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: background-color 0.3s ease;
}

.action-button:hover {
  background-color: var(--accent-color-hover, #8a6553);
}

.delete-button {
  background-color: var(--error-color, #e74c3c);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: background-color 0.3s ease;
}

.delete-button:hover {
  background-color: var(--error-hover-color, #c0392b);
}

.modal-footer-buttons {
  display: flex;
  gap: 10px;
  justify-content: space-between;
  width: 100%;
}

/* Times toggle button */
.times-toggle {
  position: fixed;
  right: 15px;
  bottom: 15px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: var(--header-bg);
  color: var(--header-text);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 10px var(--shadow-color);
  z-index: 2000; /* Higher z-index to ensure it's above the times panel */
  cursor: pointer;
  transition: all 0.2s, background-color 0.3s ease, color 0.3s ease,
    box-shadow 0.3s ease;
  border: none;
  font-size: 1.2rem;
}

/* RTL times toggle button */
[dir="rtl"] .times-toggle {
  right: auto;
  left: 15px;
}

.times-toggle:hover {
  transform: scale(1.05);
  background-color: var(--btn-hover);
}

/* Active state for times toggle */
.times-toggle.active {
  background-color: var(--btn-hover);
}

/* Modal styles (common for settings and solve details) */
.settings-modal,
.solve-details-modal,
.info-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 3000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s;
}

.settings-modal.show,
.solve-details-modal.show,
.info-modal.show {
  opacity: 1;
  visibility: visible;
}

.settings-content,
.solve-details-content {
  background-color: var(--container-bg);
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 5px 15px var(--shadow-color);
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

/* Info Modal Styles */
.info-modal-content {
  background-color: var(--container-bg);
  border-radius: 8px;
  box-shadow: 0 4px 20px var(--shadow-color);
  width: 95%;
  max-width: 1200px;
  height: 90vh;
  display: flex;
  flex-direction: column;
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

.info-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--header-bg);
  border-radius: 8px 8px 0 0;
}

.info-modal-title {
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--header-text);
  display: flex;
  align-items: center;
  gap: 10px;
}

.info-modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--header-text);
  opacity: 0.8;
  transition: opacity 0.2s ease, transform 0.2s ease;
  padding: 5px;
  border-radius: 4px;
}

.info-modal-close:hover {
  opacity: 1;
  transform: scale(1.1);
  background-color: rgba(255, 255, 255, 0.1);
}

.info-modal-body {
  padding: 25px;
  overflow-y: auto;
  flex: 1;
  scrollbar-width: thin;
  scrollbar-color: var(--accent-color) var(--card-bg);
}

.info-section {
  margin-bottom: 30px;
  background-color: var(--card-bg);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px var(--shadow-color);
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

.info-section:last-child {
  margin-bottom: 0;
}

.info-section-title {
  font-size: 1.3rem;
  font-weight: bold;
  color: var(--accent-color);
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 10px;
  border-bottom: 2px solid var(--accent-color);
  padding-bottom: 8px;
}

.info-section-content {
  color: var(--text-color);
  line-height: 1.6;
}

.info-section-content p {
  margin-bottom: 15px;
}

.info-action-btn {
  background-color: var(--accent-color);
  color: white;
  border: none;
  border-radius: 6px;
  padding: 12px 20px;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  font-size: 0.95rem;
  font-weight: 500;
  transition: all 0.3s ease;
  margin: 10px 0;
}

.info-action-btn:hover {
  background-color: var(--btn-hover);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.info-note {
  background-color: var(--bg-color);
  border-left: 4px solid var(--accent-color);
  padding: 12px 15px;
  margin: 15px 0;
  border-radius: 0 4px 4px 0;
  font-size: 0.9rem;
  color: var(--text-secondary);
  font-style: italic;
}

/* Shortcuts Grid */
.shortcuts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 15px;
}

.shortcut-category {
  background-color: var(--bg-color);
  border-radius: 6px;
  padding: 15px;
  border: 1px solid var(--border-color);
}

.shortcut-category h4 {
  color: var(--accent-color);
  margin: 0 0 12px 0;
  font-size: 1.1rem;
  font-weight: 600;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 6px;
}

.shortcut-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.shortcut-item:last-child {
  border-bottom: none;
}

.shortcut-item kbd {
  background-color: var(--card-bg);
  border: 2px solid var(--border-color);
  border-radius: 4px;
  padding: 4px 8px;
  font-family: monospace;
  font-size: 0.85rem;
  font-weight: bold;
  color: var(--accent-color);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  min-width: 80px;
  text-align: center;
}

.shortcut-item span {
  flex: 1;
  margin-left: 15px;
  color: var(--text-color);
  font-size: 0.9rem;
}

/* Mobile Gestures */
.gesture-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid var(--border-color);
}

.gesture-item:last-child {
  border-bottom: none;
}

.gesture-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--accent-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 1.2rem;
}

.gesture-description {
  flex: 1;
}

.gesture-description strong {
  display: block;
  color: var(--text-color);
  font-size: 1rem;
  margin-bottom: 4px;
}

.gesture-description span {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

/* Features Grid */
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-top: 15px;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  padding: 15px;
  background-color: var(--bg-color);
  border-radius: 6px;
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.feature-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px var(--shadow-color);
}

.feature-item i {
  font-size: 1.5rem;
  color: var(--accent-color);
  margin-top: 2px;
}

.feature-item strong {
  display: block;
  color: var(--text-color);
  font-size: 1rem;
  margin-bottom: 6px;
}

.feature-item p {
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin: 0;
  line-height: 1.4;
}

/* Sync Features */
.sync-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
  margin: 20px 0;
}

.sync-feature {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 12px;
  background-color: var(--bg-color);
  border-radius: 6px;
  border: 1px solid var(--border-color);
  font-size: 0.9rem;
  color: var(--text-color);
}

.sync-feature i {
  color: var(--accent-color);
  font-size: 1.1rem;
}

/* Settings Section Title Icons */
.settings-section-title i {
  margin-right: 8px;
  color: var(--accent-color);
  font-size: 1.1rem;
}

.settings-section-title {
  display: flex;
  align-items: center;
  font-size: 1rem;
  font-weight: bold;
  margin-bottom: 15px;
  color: var(--text-color);
}

/* Google Drive Sync Settings Styles */

.sync-status-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 15px;
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.sync-status-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
}

.sync-status-label {
  font-weight: 500;
  color: var(--text-color);
  font-size: 0.95rem;
}

.sync-status {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
}

.sync-status.connected {
  background-color: #e8f5e8;
  color: #2e7d32;
  border: 1px solid #c8e6c9;
}

.sync-status.error {
  background-color: #ffebee;
  color: #c62828;
  border: 1px solid #ffcdd2;
}

.sync-status.syncing {
  background-color: #e3f2fd;
  color: #1565c0;
  border: 1px solid #bbdefb;
  animation: pulse 2s infinite;
}

.sync-status:not(.connected):not(.error):not(.syncing) {
  background-color: var(--bg-color);
  color: var(--text-color);
  border: 1px solid var(--border-color);
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Dark mode sync status colors */
.dark-mode .sync-status.connected {
  background-color: #1b5e20;
  color: #a5d6a7;
  border-color: #2e7d32;
}

.dark-mode .sync-status.error {
  background-color: #b71c1c;
  color: #ef9a9a;
  border-color: #c62828;
}

.dark-mode .sync-status.syncing {
  background-color: #0d47a1;
  color: #90caf9;
  border-color: #1565c0;
}

.sync-connection-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.sync-connect-btn,
.sync-disconnect-btn {
  background-color: var(--accent-color);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 0.9rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.3s ease;
}

.sync-connect-btn:hover,
.sync-disconnect-btn:hover {
  background-color: var(--accent-color-hover);
}

.sync-disconnect-btn {
  background-color: #e74c3c;
}

.sync-disconnect-btn:hover {
  background-color: #c0392b;
}

.sync-actions {
  display: inline;
  gap: 10px;
}

.sync-action-btn {
  background-color: var(--accent-color);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  margin: 10px;
  font-size: 0.9rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.3s ease;
  flex: 1;
}

.sync-action-btn:hover {
  background-color: var(--accent-color-hover);
}

.sync-action-btn i {
  font-size: 1.1rem;
}

.sync-auto-option {
  margin: 0 10px !important;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.sync-auto-option .checkbox-label {
  margin-bottom: 0;
}

.setting-note {
  font-size: 0.85rem;
  color: var(--text-color);
  opacity: 0.8;
  line-height: 1.4;
  margin-top: 0;
  margin-left: 20px;
  display: block;
}

/* Responsive design for sync section */
@media (max-width: 768px) {
  .settings-section-title {
    font-size: 0.95rem;
  }

  .settings-section-title i {
    font-size: 1rem;
    margin-right: 6px;
  }

  .sync-status-container {
    padding: 12px;
    gap: 10px;
  }

  .sync-actions {
    flex-direction: column;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .sync-status-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .sync-connection-buttons {
    width: 100%;
    flex-direction: column;
    gap: 8px;
  }

  .sync-connect-btn,
  .sync-disconnect-btn {
    width: 100%;
    justify-content: center;
  }

  .sync-actions {
    flex-direction: column;
    gap: 8px;
  }

  .sync-action-btn {
    width: 100%;
    justify-content: center;
  }

  .settings-section-title {
    font-size: 0.9rem;
    margin-bottom: 12px;
  }

  .settings-section-title i {
    font-size: 0.9rem;
    margin-right: 5px;
  }

  .setting-note {
    font-size: 0.8rem;
    margin-left: 18px;
  }
}

/* Info Modal Responsive Styles */
@media (max-width: 768px) {
  .info-modal-content {
    width: 98%;
    height: 95vh;
    margin: 2.5vh 1%;
  }

  .info-modal-header {
    padding: 15px 20px;
  }

  .info-modal-title {
    font-size: 1.3rem;
  }

  .info-modal-body {
    padding: 20px;
  }

  .shortcuts-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .sync-features {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .shortcut-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .shortcut-item kbd {
    min-width: auto;
    align-self: flex-start;
  }

  .shortcut-item span {
    margin-left: 0;
  }

  .info-section {
    padding: 15px;
    margin-bottom: 20px;
  }

  .info-section-title {
    font-size: 1.2rem;
  }
}

@media (max-width: 480px) {
  .info-modal-content {
    width: 100%;
    height: 100vh;
    border-radius: 0;
    margin: 0;
  }

  .info-modal-header {
    border-radius: 0;
    padding: 12px 15px;
  }

  .info-modal-title {
    font-size: 1.2rem;
  }

  .info-modal-body {
    padding: 15px;
  }

  .info-section {
    padding: 12px;
    margin-bottom: 15px;
  }

  .gesture-item {
    padding: 10px 0;
  }

  .gesture-icon {
    width: 35px;
    height: 35px;
    font-size: 1rem;
  }

  .feature-item {
    padding: 12px;
  }
}

.settings-header,
.solve-details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid var(--border-color);
  transition: border-color 0.3s ease;
}

.settings-title,
.solve-details-title {
  font-size: 1.2rem;
  font-weight: bold;
  color: var(--text-color);
  transition: color 0.3s ease;
}

.settings-close,
.solve-details-close {
  background: transparent;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  color: var(--text-color);
  opacity: 0.7;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.3s ease, color 0.3s ease, opacity 0.3s ease;
}

.settings-close:hover,
.solve-details-close:hover {
  background-color: var(--card-bg);
  opacity: 1;
}

.settings-body {
  padding: 20px;
  overflow-y: auto;
  flex: 1;
}

.solve-details-body {
  padding: 15px;
  overflow-y: auto;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.settings-section,
.solve-details-section {
  margin-bottom: 15px;
  width: 100%;
  box-sizing: border-box;
}

.solve-details-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.settings-section-title,
.solve-details-section-title {
  font-size: 1rem;
  font-weight: bold;
  margin-bottom: 10px;
  color: var(--text-color);
  transition: color 0.3s ease;
}

.copy-scramble-btn,
#stat-detail-copy-scramble {
  background: none;
  border: none;
  color: var(--text-color);
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.9rem;
  transition: background-color 0.2s ease, color 0.2s ease;
}

.copy-scramble-btn:hover,
#stat-detail-copy-scramble:hover {
  background-color: var(--hover-bg);
  color: var(--accent-color);
}

.copy-scramble-btn:active,
#stat-detail-copy-scramble:active {
  transform: scale(0.95);
}

/* Ensure scrambles are always LTR direction and center-aligned */
.solve-details-scramble,
.scramble,
.fmc-scramble {
  direction: ltr !important;
  text-align: center !important;
}

.settings-option {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 0.9rem;
  color: var(--text-color);
  cursor: pointer;
  transition: color 0.3s ease;
}

.checkbox-label.disabled,
.checkbox-label input:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.setting-note {
  font-size: 0.75rem;
  color: var(--text-color);
  opacity: 0.7;
  margin-top: 5px;
  margin-left: 28px; /* Align with checkbox text */
  font-style: italic;
  transition: color 0.3s ease, opacity 0.3s ease;
}

/* Slider styles */
.slider-option {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.slider-label {
  display: flex;
  flex-direction: column;
  gap: 10px;
  font-size: 0.9rem;
  color: var(--text-color);
  width: 100%;
  transition: color 0.3s ease;
}

.slider-container {
  display: flex;
  align-items: center;
  gap: 15px;
  width: 100%;
}

input[type="range"] {
  -webkit-appearance: none;
  width: 100%;
  height: 6px;
  background: var(--border-color);
  border-radius: 3px;
  outline: none;
  transition: background 0.3s ease;
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #a57865;
  cursor: pointer;
  transition: background 0.3s ease;
}

input[type="range"]::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #a57865;
  cursor: pointer;
  border: none;
  transition: background 0.3s ease;
}

.slider-value {
  min-width: 40px;
  text-align: center;
  font-family: monospace;
  font-weight: bold;
}

/* Custom Checkbox Styling */
.checkbox-label input[type="checkbox"] {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  width: 18px;
  height: 18px;
  border: 2px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--container-bg);
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;
  margin: 0;
  flex-shrink: 0;
}

.checkbox-label input[type="checkbox"]:hover {
  border-color: var(--accent-color);
  background-color: var(--hover-bg);
}

.checkbox-label input[type="checkbox"]:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 2px rgba(165, 120, 101, 0.2);
}

.checkbox-label input[type="checkbox"]:checked {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
}

.checkbox-label input[type="checkbox"]:checked::after {
  content: "";
  position: absolute;
  left: 5px;
  top: 2px;
  width: 4px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.checkbox-label input[type="checkbox"]:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: var(--card-bg);
  border-color: var(--border-color);
}

.checkbox-label input[type="checkbox"]:disabled:checked {
  background-color: var(--border-color);
}

.settings-footer,
.solve-details-footer {
  padding: 15px 20px;
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  gap: 10px;
  transition: border-color 0.3s ease;
}

.solve-details-footer .action-button {
  min-width: 100px;
}

/* Solve details specific styles */
.solve-details-time {
  font-size: 2.5rem;
  font-weight: bold;
  font-family: monospace;
  text-align: center;
  margin-bottom: 5px;
  color: var(--text-color);
  transition: color 0.3s ease;
}

.solve-details-date {
  text-align: center;
  font-size: 0.9rem;
  color: var(--text-color);
  opacity: 0.7;
  margin-bottom: 20px;
  transition: color 0.3s ease;
}

.solve-details-scramble {
  font-family: monospace;
  font-size: calc(
    1.5rem * var(--font-scale)
  ); /* Base font size with scale factor */
  background-color: var(--card-bg);
  color: var(--text-color);
  padding: 10px;
  border-radius: 4px;
  word-wrap: break-word;
  word-break: break-all;
  overflow-wrap: break-word;
  line-height: 1.7; /* Increased line height */
  letter-spacing: 0.1em; /* Added letter spacing */
  margin-bottom: 10px;
  max-width: 100%;
  overflow-x: hidden;
  box-shadow: 0 1px 3px var(--shadow-color);
  transition: background-color 0.3s ease, color 0.3s ease, box-shadow 0.3s ease;
}

/* Puzzle-specific font sizes for solve details */
.solve-details-scramble.event-222 {
  font-size: calc(1.9rem * var(--font-scale)); /* 2x2 */
}

.solve-details-scramble.event-333,
.solve-details-scramble.event-333oh,
.solve-details-scramble.event-333fm,
.solve-details-scramble.event-333bf {
  font-size: calc(1.8rem * var(--font-scale)); /* 3x3 and variants */
}

.solve-details-scramble.event-444,
.solve-details-scramble.event-444bf {
  font-size: calc(1.7rem * var(--font-scale)); /* 4x4 */
}

.solve-details-scramble.event-555,
.solve-details-scramble.event-555bf {
  font-size: calc(1.6rem * var(--font-scale)); /* 5x5 */
}

.solve-details-scramble.event-666,
.solve-details-scramble.event-777 {
  font-size: calc(1.3rem * var(--font-scale)); /* 6x6 and 7x7 */
  letter-spacing: 0.08em; /* Slightly reduced letter spacing for long scrambles */
}

.solve-details-scramble.event-skewb,
.solve-details-scramble.event-pyram {
  font-size: calc(1.8rem * var(--font-scale)); /* Skewb and Pyraminx */
}

.solve-details-scramble.event-sq1 {
  font-size: calc(1.7rem * var(--font-scale)); /* Square-1 */
}

.solve-details-scramble.event-clock {
  font-size: calc(1.8rem * var(--font-scale)); /* Clock */
}

.solve-details-scramble.event-minx {
  font-size: calc(1.4rem * var(--font-scale)); /* Megaminx */
}

.solve-details-penalty {
  display: flex;
  gap: 15px;
  margin-top: 5px;
  flex-wrap: wrap;
}

.radio-label {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 0.9rem;
  color: var(--text-color);
  cursor: pointer;
  transition: color 0.3s ease;
}

/* Custom Radio Button Styling */
.radio-label input[type="radio"] {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  width: 16px;
  height: 16px;
  border: 2px solid var(--border-color);
  border-radius: 50%;
  background-color: var(--container-bg);
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;
  margin: 0;
  flex-shrink: 0;
}

.radio-label input[type="radio"]:hover {
  border-color: var(--accent-color);
  background-color: var(--hover-bg);
}

.radio-label input[type="radio"]:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 2px rgba(165, 120, 101, 0.2);
}

.radio-label input[type="radio"]:checked {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
}

.radio-label input[type="radio"]:checked::after {
  content: "";
  position: absolute;
  left: 50%;
  top: 50%;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: white;
  transform: translate(-50%, -50%);
}

.radio-label input[type="radio"]:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: var(--card-bg);
  border-color: var(--border-color);
}

.radio-label input[type="radio"]:disabled:checked {
  background-color: var(--border-color);
}

/* General checkbox and radio styling for any inputs not in specific label classes */
input[type="checkbox"]:not(.checkbox-label input[type="checkbox"]) {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  width: 18px;
  height: 18px;
  border: 2px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--container-bg);
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;
  margin: 0 8px 0 0;
  flex-shrink: 0;
}

input[type="checkbox"]:not(.checkbox-label input[type="checkbox"]):hover {
  border-color: var(--accent-color);
  background-color: var(--hover-bg);
}

input[type="checkbox"]:not(.checkbox-label input[type="checkbox"]):focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 2px rgba(165, 120, 101, 0.2);
}

input[type="checkbox"]:not(.checkbox-label input[type="checkbox"]):checked {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
}

input[type="checkbox"]:not(
    .checkbox-label input[type="checkbox"]
  ):checked::after {
  content: "";
  position: absolute;
  left: 5px;
  top: 2px;
  width: 4px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

input[type="radio"]:not(.radio-label input[type="radio"]) {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  width: 16px;
  height: 16px;
  border: 2px solid var(--border-color);
  border-radius: 50%;
  background-color: var(--container-bg);
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;
  margin: 0 8px 0 0;
  flex-shrink: 0;
}

input[type="radio"]:not(.radio-label input[type="radio"]):hover {
  border-color: var(--accent-color);
  background-color: var(--hover-bg);
}

input[type="radio"]:not(.radio-label input[type="radio"]):focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 2px rgba(165, 120, 101, 0.2);
}

input[type="radio"]:not(.radio-label input[type="radio"]):checked {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
}

input[type="radio"]:not(.radio-label input[type="radio"]):checked::after {
  content: "";
  position: absolute;
  left: 50%;
  top: 50%;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: white;
  transform: translate(-50%, -50%);
}

#solve-details-comment {
  width: 100%;
  height: 80px; /* Fixed height instead of min-height */
  padding: 10px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-family: inherit;
  font-size: 0.9rem;
  resize: none; /* Prevent resizing which can cause scrolling issues */
  box-sizing: border-box; /* Include padding in width/height calculation */
  background-color: var(--container-bg);
  color: var(--text-color);
  transition: border-color 0.3s ease, background-color 0.3s ease,
    color 0.3s ease;
}

/* Debug info */
.debug-info {
  position: fixed;
  bottom: 10px;
  right: 10px;
  padding: 10px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  border-radius: 4px;
  text-align: left;
  font-family: monospace;
  font-size: 0.7rem;
  display: none;
  max-width: 300px;
  z-index: 50;
}

.debug-info.show {
  display: block;
}

.hidden {
  display: none !important;
}

/* Mobile-only elements */
.mobile-only {
  display: none;
}

/* All Events View */
.all-events-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1rem 0;
}

.event-summary-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  background-color: var(--card-bg);
  border-radius: 8px;
  border: 1px solid var(--border-color);
  transition: all 0.2s ease;
}

.event-summary-item:hover {
  background-color: var(--accent-color);
  transform: translateY(-2px);
  border-color: var(--header-bg);
}

.event-summary-item:hover .event-name,
.event-summary-item:hover .event-stat-value {
  color: white;
}

.event-name {
  font-weight: 600;
  font-size: 1.1rem;
  color: var(--text-color);
  min-width: 120px;
}

.event-stats {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.event-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  min-width: 80px;
}

.event-stat:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.event-stat-label {
  font-size: 0.8rem;
  color: var(--text-secondary);
  margin-bottom: 0.25rem;
  font-weight: 500;
}

.event-stat-value {
  font-size: 1rem;
  font-weight: 600;
  color: var(--accent-color);
}

.event-stat-value.no-data {
  color: var(--text-secondary);
  font-style: italic;
}

/* Statistics Details Panel - Mobile Responsive */
@media (max-width: 768px) {
  .stats-details-panel {
    height: 100vh;
  }

  .stats-details-header {
    padding: 15px 20px;
    min-height: 50px;
  }

  .stats-details-title-container {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  /* Stack rows vertically on mobile */
  .stats-row {
    flex-direction: column;
    gap: 1rem;
  }

  /* All Events View Mobile */
  .event-summary-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
  }

  .event-name {
    min-width: auto;
    width: 100%;
    text-align: center;
  }

  .event-stats {
    width: 100%;
    justify-content: space-around;
    gap: 1rem;
  }

  .event-stat {
    min-width: auto;
    flex: 1;
  }

  .stats-details-title-text {
    font-size: 1.2rem;
  }

  .stats-event-selector-btn {
    min-width: 130px;
    max-width: 220px;
    font-size: 0.9rem;
  }

  .stats-details-content {
    padding: 15px 20px;
  }

  .stats-section {
    margin-bottom: 20px;
    padding: 15px;
  }

  .stats-section-title {
    font-size: 1.1rem;
    margin-bottom: 12px;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
  }

  .stat-card {
    padding: 12px;
  }

  .stat-card-value {
    font-size: 1.1rem;
  }

  .analysis-grid,
  .predictions-grid {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .chart-container {
    padding: 15px;
    min-height: 200px;
  }

  .record-item {
    padding: 10px 12px;
  }

  .record-label {
    font-size: 0.9rem;
  }

  .record-value {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .stats-details-header {
    padding: 12px 15px;
  }

  .stats-details-content {
    padding: 12px 15px;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .stat-card-value {
    font-size: 1rem;
  }

  .chart-container {
    min-height: 180px;
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .main-content {
    /* Keep the same layout as desktop */
    grid-template-areas:
      "scramble scramble"
      "timer timer"
      "stats visualization";
    grid-template-rows: auto 1fr auto;
  }

  .timer,
  .inspection,
  .inspection.ready {
    font-size: 4rem;
  }

  .stats-container,
  .visualization {
    height: auto;
    min-height: 150px;
  }

  .times-panel {
    width: 100%;
    left: -100%;
  }

  /* Show mobile-only elements */
  .mobile-only {
    display: block;
  }

  /* Keep times toggle visible even when panel is open */
  .times-toggle {
    z-index: 2500; /* Even higher z-index on mobile */
    bottom: 15px;
    right: 15px;
    position: fixed;
  }

  /* Hide button text on mobile to save space in times panel footer */
  .times-panel-footer .footer-buttons button span,
  .times-panel-footer .footer-actions button span {
    display: none;
  }

  /* Adjust button padding for icon-only display on mobile */
  #clear-times-btn,
  #edit-session-btn,
  #stats-btn {
    padding: 8px;
    min-width: 36px;
    gap: 0;
  }

  /* RTL mobile fixes */
  [dir="rtl"] .times-toggle {
    z-index: 2500; /* Ensure it stays above other elements */
  }

  [dir="rtl"] .times-panel {
    left: auto;
    right: -100%;
    transition: right 0.3s ease, background-color 0.3s ease,
      box-shadow 0.3s ease;
  }

  [dir="rtl"] .times-panel.show {
    left: auto;
    right: 0;
  }

  .header-center {
    gap: 5px;
  }

  /* Mobile RTL adjustments */
  [dir="rtl"] .header-right {
    right: auto;
    left: 10px;
  }

  [dir="rtl"] .header-left {
    left: auto;
    right: 10px;
  }

  .header-center {
    flex-wrap: wrap;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .timer,
  .inspection,
  .inspection.ready {
    font-size: 3.5rem;
  }

  /* Fix manual input container on mobile */
  .manual-input-container {
    width: 90%;
    max-width: 100%;
    margin: 20px auto;
    left: 0;
    right: 0;
    position: relative;
  }

  /* Ensure the timer container centers its content */
  .timer-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
  }

  .manual-time-input {
    font-size: 1.8rem;
    padding: 15px 50px 15px 15px; /* Increase right padding for the inspection timer */
    max-width: 250px; /* Smaller max-width for mobile */
  }

  .manual-input-wrapper {
    width: 100%;
    display: flex;
    justify-content: center;
  }

  .input-container {
    max-width: 250px;
  }

  .manual-inspection {
    font-size: 1.3rem;
    right: 15px;
  }

  /* Adjust all scramble font sizes for mobile */
  .scramble {
    font-size: calc(
      1.4rem * var(--font-scale)
    ); /* Mobile base font size with scale factor */
    letter-spacing: 0.08em; /* Added letter spacing for mobile */
    line-height: 1.6; /* Increased line height for mobile */
  }

  /* Mobile puzzle-specific font sizes */
  .scramble.event-222 {
    font-size: calc(1.8rem * var(--font-scale)); /* 2x2 */
  }

  .scramble.event-333,
  .scramble.event-333oh,
  .scramble.event-333fm,
  .scramble.event-333bf {
    font-size: calc(1.7rem * var(--font-scale)); /* 3x3 and variants */
  }

  .scramble.event-444,
  .scramble.event-444bf,
  .scramble.event-sq1,
  .scramble.event-clock {
    font-size: calc(1.6rem * var(--font-scale)); /* 4x4, Square-1, Clock */
  }

  .scramble.event-555,
  .scramble.event-555bf {
    font-size: calc(1.5rem * var(--font-scale)); /* 5x5 */
  }

  .scramble.event-666,
  .scramble.event-777 {
    font-size: calc(1.3rem * var(--font-scale)); /* 6x6 and 7x7 */
    letter-spacing: 0.06em; /* Further reduced letter spacing for mobile */
  }

  .scramble.event-minx {
    font-size: calc(1.4rem * var(--font-scale)); /* Megaminx */
  }

  .scramble.event-skewb,
  .scramble.event-pyram {
    font-size: calc(1.8rem * var(--font-scale)); /* Skewb and Pyraminx */
  }

  .settings-content,
  .solve-details-content {
    width: 95%;
    max-height: 90vh;
  }
}
.container {
  transform: scale(1) !important;
}
/* Scrollbar styling */
.times-list,
.solve-details-body,
.settings-body {
  scrollbar-width: thin;
  scrollbar-color: var(--border-color) var(--card-bg);
}

/* Dark mode scrollbar */
.dark-mode .times-list,
.dark-mode .solve-details-body,
.dark-mode .settings-body {
  scrollbar-color: #a57865 #2d2d2d;
}

/* Hidden elements */
/* Audio elements are now handled by the audio-manager.js */

/* MBLD Modal Styles */
.mbld-modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 3000;
  overflow: auto;
}

.mbld-modal.show {
  display: flex;
  justify-content: center;
  align-items: center;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.mbld-modal-content {
  background-color: var(--container-bg);
  border-radius: 8px;
  box-shadow: 0 4px 20px var(--shadow-color);
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

.mbld-scrambles-content,
.mbld-visualizations-content {
  max-width: 800px;
  width: 95%;
  height: 90vh;
}

.mbld-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid var(--border-color);
}

.mbld-modal-title {
  font-size: 1.2rem;
  font-weight: bold;
  color: var(--text-color);
}

.mbld-modal-close {
  background: none;
  border: none;
  font-size: 1.2rem;
  color: var(--text-color);
  cursor: pointer;
  padding: 5px;
  transition: color 0.3s ease;
}

.mbld-modal-close:hover {
  color: #e74c3c;
}

.mbld-modal-body {
  padding: 20px;
  overflow: hidden scroll;
  flex-grow: 1;
}

.mbld-modal-footer {
  padding: 15px 20px;
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end;
}

.mbld-input-group {
  margin-bottom: 15px;
}

.mbld-input-group label {
  display: block;
  margin-bottom: 5px;
  color: var(--text-color);
}

.mbld-input {
  width: 50%;
  padding: 10px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--container-bg);
  color: var(--text-color);
  font-size: 1rem;
  transition: border-color 0.3s ease, background-color 0.3s ease;
}

.mbld-input:focus {
  outline: none;
  border-color: var(--header-bg);
}

.mbld-info {
  margin-top: 15px;
  padding: 10px;
  background-color: var(--card-bg);
  border-radius: 4px;
  color: var(--text-color);
  text-align: center;
  font-size: 14px;
}

.mbld-info.warning {
  background-color: #fff3cd;
  color: #856404;
  font-weight: bold;
}

.mbld-info p {
  margin: 5px 0;
}

/* MBLD Scrambles Container */
.mbld-scramble-item {
  margin-bottom: 20px;
  padding: 15px;
  background-color: var(--card-bg);
  border-radius: 8px;
  transition: background-color 0.3s ease;
}

.mbld-scramble-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.mbld-scramble-number {
  font-weight: bold;
  color: var(--text-color);
}

.mbld-scramble-text {
  font-family: monospace;
  font-size: 1.1rem;
  margin-bottom: 15px;
  word-wrap: break-word;
  line-height: 1.5;
  color: var(--text-color);
}

/* MBLD Visualizations Container */
.mbld-visualizations-wrapper {
  width: 100%;
  padding: 10px;
}

.mbld-visualizations-title {
  text-align: center;
  margin-bottom: 20px;
  color: var(--text-color);
  font-size: 1.2rem;
}

.mbld-visualization-item {
  margin-bottom: 30px;
  padding: 15px;
  background-color: var(--card-bg);
  border-radius: 8px;
  transition: background-color 0.3s ease;
  box-shadow: 0 2px 5px var(--shadow-color);
}

.mbld-visualization-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 5px;
}

.mbld-visualization-number {
  font-weight: bold;
  color: var(--text-color);
  font-size: 1.1rem;
}

/* Two-column layout for visualization modal */
.mbld-two-column-container {
  display: flex;
  flex-direction: row;
  gap: 15px;
  width: 100%;
}

.mbld-scramble-column {
  flex: 1;
  min-width: 0; /* Prevent overflow */
}

.mbld-visualization-column {
  flex: 1;
  min-width: 0; /* Prevent overflow */
}

.mbld-visualization-scramble {
  font-family: monospace;
  font-size: 0.9rem;
  word-wrap: break-word;
  line-height: 1.5;
  color: var(--text-color);
  background-color: var(--bg-color-secondary);
  padding: 8px;
  border-radius: 4px;
  height: 100%;
  overflow-y: auto;
  max-height: 200px;
}

.mbld-cube-visualization {
  width: 100%;
  height: 200px;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  background-color: var(--bg-color);
  border-radius: 4px;
  border: 1px solid var(--border-color);
}

.mbld-cube-visualization twisty-player {
  width: 100%;
  height: 100%;
}

/* Responsive layout for small screens */
@media (max-width: 768px) {
  .mbld-two-column-container {
    flex-direction: column;
  }

  .mbld-visualization-scramble {
    max-height: 100px;
    margin-bottom: 10px;
  }

  .mbld-cube-visualization {
    height: 180px;
  }
}

.mbld-visualization {
  width: 100%;
  height: 200px;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Make scramble area clickable for MBLD */
.scramble.mbld-clickable {
  cursor: pointer;
  position: relative;
}

/* Removed the after pseudo-element that was adding the text */

/* MBLD Result Display */
.mbld-result {
  display: flex;
  align-items: center;
}

.mbld-score {
  font-weight: bold;
  margin-right: 10px;
}

.mbld-time {
  color: var(--text-color);
}

/* FMC Modal Styles */
.fmc-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.fmc-modal.show {
  opacity: 1;
  visibility: visible;
}

.fmc-modal-content {
  background-color: var(--card-bg);
  border-radius: 8px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  transition: transform 0.3s ease;
  transform: translateY(-20px);
}

.fmc-modal.show .fmc-modal-content {
  transform: translateY(0);
}

.fmc-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid var(--border-color);
}

.fmc-modal-title {
  font-size: 1.2rem;
  font-weight: bold;
  color: var(--text-color);
}

.fmc-modal-close {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  color: var(--text-color);
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.fmc-modal-close:hover {
  opacity: 1;
}

.fmc-modal-body {
  padding: 20px;
  overflow-y: auto;
}

.fmc-modal-footer {
  padding: 15px 20px;
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end;
}

.fmc-info {
  margin-bottom: 20px;
  padding: 15px;
  background-color: var(--bg-color-secondary);
  border-radius: 6px;
  color: var(--text-color);
  font-size: 0.95rem;
  line-height: 1.5;
}

.fmc-timer-container {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding: 10px 15px;
  background-color: var(--bg-color-secondary);
  border-radius: 6px;
}

.fmc-timer-label {
  font-weight: bold;
  margin-right: 10px;
  color: var(--text-color);
}

.fmc-timer {
  font-size: 1.5rem;
  font-family: monospace;
  color: var(--text-color);
}

.fmc-scramble-section {
  margin-bottom: 20px;
}

.fmc-scramble-label {
  font-weight: bold;
  margin-bottom: 10px;
  color: var(--text-color);
}

.fmc-scramble {
  padding: 15px;
  background-color: var(--bg-color-secondary);
  border-radius: 6px;
  font-family: monospace;
  font-size: 1.1rem;
  line-height: 1.5;
  word-wrap: break-word;
  color: var(--text-color);
}

.fmc-solution-section {
  margin-bottom: 20px;
}

.fmc-solution-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.fmc-solution-label {
  font-weight: bold;
  color: var(--text-color);
}

.fmc-move-count-container {
  color: var(--text-color);
  font-size: 0.9rem;
}

.fmc-solution-input {
  width: 100%;
  padding: 15px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background-color: var(--bg-color);
  color: var(--text-color);
  font-family: monospace;
  font-size: 1rem;
  resize: vertical;
  min-height: 150px;
  transition: border-color 0.3s ease;
}

.fmc-solution-input:focus {
  outline: none;
  border-color: var(--accent-color);
}

.fmc-validation {
  margin-top: 10px;
  min-height: 20px;
  font-size: 0.9rem;
}

.fmc-validation.error {
  color: #e74c3c;
}

.fmc-validation.success {
  color: #2ecc71;
}

.fmc-validation.warning {
  color: #f39c12;
}

.fmc-notation-help {
  margin-top: 20px;
  padding: 15px;
  background-color: var(--bg-color-secondary);
  border-radius: 6px;
}

.fmc-notation-help-title {
  font-weight: bold;
  margin-bottom: 10px;
  color: var(--text-color);
}

.fmc-notation-help-content {
  font-size: 0.9rem;
  line-height: 1.6;
  color: var(--text-color);
}

/* FMC Result Display */
.fmc-result {
  display: flex;
  align-items: center;
}

.fmc-move-count {
  font-weight: bold;
  margin-right: 10px;
  color: var(--text-color);
}

.fmc-time {
  color: var(--text-color);
  font-size: 0.9rem;
  opacity: 0.8;
}

/* FMC Details in Solve Details Modal */
.fmc-details-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.fmc-details-section {
  padding: 10px;
  background-color: var(--bg-color-secondary);
  border-radius: 6px;
}

.fmc-details-header {
  font-weight: bold;
  margin-bottom: 8px;
  color: var(--text-color);
}

.fmc-details-scramble,
.fmc-details-solution {
  font-family: monospace;
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.5;
  color: var(--text-color);
  margin-bottom: 10px;
}

.fmc-details-move-count {
  font-weight: bold;
  color: var(--accent-color);
  margin-top: 5px;
  font-size: 0.9rem;
}

/* MBLD Solve Details Scrambles */
.mbld-scrambles-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 10px;
  margin-top: 10px;
}

.mbld-solve-details-scramble-item {
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--border-color);
}

.mbld-solve-details-scramble-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.mbld-solve-details-scramble-header {
  font-weight: bold;
  margin-bottom: 5px;
  color: var(--text-color);
}

.mbld-solve-details-scramble-text {
  font-family: monospace;
  word-wrap: break-word;
  line-height: 1.5;
  color: var(--text-color);
}

/* Webkit scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--card-bg);
}

::-webkit-scrollbar-thumb {
  background-color: var(--border-color);
  border-radius: 4px;
}

.dark-mode ::-webkit-scrollbar-thumb {
  background-color: #a57865;
}

/* Custom Modal Styles */
#custom-modal-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10000;
  pointer-events: none;
}

.custom-modal,
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: 10001;
  visibility: hidden;
  padding: 10px;
  box-sizing: border-box;
}

.custom-modal.show,
.modal.show {
  opacity: 1;
  pointer-events: auto;
  visibility: visible;
}

.custom-modal-content,
.modal-content {
  background-color: var(--card-bg);
  border-radius: 8px;
  padding: 20px;
  width: 90%;
  max-width: 400px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  transform: translateY(-20px);
  transition: transform 0.3s ease;
  color: var(--text-color);
}

.custom-modal.show .custom-modal-content,
.modal.show .modal-content {
  transform: translateY(0);
}

.custom-modal-title,
.modal-title {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 1.2rem;
  color: var(--text-color);
  font-weight: bold;
}

/* Modal header, body, footer */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 10px;
  flex-shrink: 0;
}

.modal-body {
  overflow-y: auto;
  flex: 1;
  min-height: 0;
  scroll-behavior: smooth;
  /* Custom scrollbar styling */
  scrollbar-width: thin;
  scrollbar-color: var(--accent-color) var(--card-bg);
}

/* Webkit scrollbar styling */
.modal-body::-webkit-scrollbar {
  width: 8px;
}

.modal-body::-webkit-scrollbar-track {
  background: var(--card-bg);
  border-radius: 4px;
}

.modal-body::-webkit-scrollbar-thumb {
  background: var(--accent-color);
  border-radius: 4px;
}

.modal-body::-webkit-scrollbar-thumb:hover {
  background: var(--header-bg);
}

/* Scroll indicator for modal content */
.modal-content::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 20px;
  background: linear-gradient(transparent, var(--card-bg));
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.modal-content.has-scroll::after {
  opacity: 1;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.2rem;
  color: var(--text-color);
  cursor: pointer;
  padding: 5px;
  transition: color 0.3s ease;
  width: auto;
  height: auto;
}

.modal-close:hover {
  color: #e74c3c;
  background: none;
  transform: none;
  box-shadow: none;
}

.modal-body {
  margin-bottom: 20px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid var(--border-color);
  padding-top: 15px;
}

.custom-modal-message {
  margin-bottom: 20px;
  line-height: 1.5;
}

.custom-modal-input-container,
.input-group {
  margin-bottom: 20px;
}

.custom-modal-input {
  width: 100%;
  padding: 10px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--bg-color);
  color: var(--text-color);
  font-size: 1rem;
}

.session-input {
  padding: 10px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--bg-color);
  color: var(--text-color);
  font-size: 1rem;
}

/* Styling for session puzzle dropdown */
#session-puzzle {
  padding-right: 30px; /* Extra space for the dropdown arrow */
}

#session-puzzle optgroup {
  font-weight: bold;
  color: var(--text-color);
  background-color: var(--bg-color-secondary);
}

/* Custom styling for the session puzzle dropdown */
.session-puzzle-dropdown {
  position: relative;
  width: 100%;
}

.session-puzzle-dropdown .session-input {
  width: 100%;
}

/* Style for the custom dropdown */
.custom-dropdown-container {
  position: relative;
  width: 100%;
}

.custom-dropdown-selected {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--bg-color);
  color: var(--text-color);
  cursor: pointer;
}

.custom-dropdown-selected i {
  margin-right: 8px;
}

.custom-dropdown-options {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  max-height: 300px;
  overflow-y: auto;
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  z-index: 1000;
  display: none;
}

.custom-dropdown-options.show {
  display: block;
}

.custom-dropdown-option {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  cursor: pointer;
}

.custom-dropdown-option:hover {
  background-color: var(--bg-color-secondary);
}

.custom-dropdown-group-label {
  font-weight: bold;
  padding: 8px 12px;
  background-color: var(--bg-color-secondary);
  color: var(--text-color);
}

/* We're using the official cubing-icon classes for non-WCA puzzles */

/* Input group styling */
.input-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.input-group label {
  font-weight: bold;
  color: var(--text-color);
}

.custom-modal-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.custom-modal-button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s ease;
}

.custom-modal-confirm {
  background-color: var(--accent-color);
  color: #ffffff; /* Always white text for better contrast */
}

.custom-modal-confirm:hover {
  background-color: var(--accent-color-hover);
}

.custom-modal-cancel {
  background-color: var(--bg-color-secondary);
  color: var(--text-color);
  border: 1px solid var(--border-color); /* Add border for better visibility */
}

.custom-modal-cancel:hover {
  background-color: var(--border-color);
}

/* FMC result styling in times list */
.fmc-result {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.fmc-move-count {
  font-weight: bold;
  color: var(--timer-color);
}

.fmc-time {
  font-size: 0.8rem;
  opacity: 0.8;
  margin-top: 2px;
}

/* Stat Detail Modal */
#stat-detail-modal .modal-content {
  max-width: 800px;
  width: 95%;
  max-height: 90vh;
}

/* Mobile responsive adjustments for modals */
@media (max-width: 768px) {
  .modal-content {
    width: 95%;
    max-width: none;
    margin: 10px;
    padding: 15px;
    max-height: 95vh;
  }

  #stat-detail-modal .modal-content {
    width: 98%;
    margin: 5px;
    max-height: 95vh;
  }

  .stat-detail-info {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .stat-detail-duration-info {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .stat-detail-scramble-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .stat-detail-scramble-text {
    margin-right: 0;
    word-break: break-word;
  }

  .stat-detail-scramble-time {
    min-width: auto;
    text-align: left;
    font-size: 1rem;
  }
}

.stat-detail-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.stat-detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background-color: var(--card-bg);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.stat-detail-label {
  font-weight: 500;
  color: var(--text-color);
}

.stat-detail-value {
  font-weight: bold;
  color: var(--accent-color);
}

.stat-detail-scramble-section {
  margin-bottom: 1.5rem;
}

.stat-detail-scramble-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.stat-detail-scramble {
  padding: 1rem;
  background-color: var(--card-bg);
  border-radius: 8px;
  border: 1px solid var(--border-color);
  font-family: monospace;
  font-size: 0.9rem;
  line-height: 1.4;
  word-break: break-all;
  text-align: center;
}

.stat-detail-average-info {
  padding: 1rem;
  background-color: var(--card-bg);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.stat-detail-duration-info {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.stat-detail-datetime-range {
  grid-column: 1 / -1;
}

.stat-detail-datetime-range .stat-detail-value {
  font-size: 0.9rem;
  line-height: 1.4;
  word-break: break-word;
}

.stat-detail-scrambles-section {
  margin-top: 1.5rem;
}

.stat-detail-scrambles-list {
  margin-top: 0.5rem;
}

.stat-detail-scramble-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  margin-bottom: 0.5rem;
  background-color: var(--bg-color);
  border-radius: 6px;
  border: 1px solid var(--border-color);
  font-family: monospace;
  font-size: 0.9rem;
}

.stat-detail-scramble-text {
  flex: 1;
  margin-right: 1rem;
  word-break: break-all;
}

.stat-detail-scramble-time {
  font-weight: bold;
  color: var(--accent-color);
  min-width: 80px;
  text-align: right;
}

.stat-detail-scramble-time.best {
  color: #28a745;
}

.stat-detail-scramble-time.worst {
  color: #dc3545;
}

.stat-detail-times-list {
  margin-bottom: 1rem;
}

.stat-detail-times {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.stat-detail-time-item {
  padding: 0.5rem;
  background-color: var(--bg-color);
  border-radius: 4px;
  text-align: center;
  font-family: monospace;
  font-size: 0.9rem;
}

.stat-detail-time-item.best {
  color: #28a745;
  font-weight: bold;
}

.stat-detail-time-item.worst {
  color: #dc3545;
  font-weight: bold;
}

/* Make clickable statistics have hover effect */
.stat-card[style*="cursor: pointer"],
.record-item[style*="cursor: pointer"] {
  transition: background-color 0.2s ease, transform 0.1s ease,
    border-color 0.2s ease;
}

.stat-card[style*="cursor: pointer"]:hover,
.record-item[style*="cursor: pointer"]:hover {
  background-color: var(--accent-color);
  transform: translateY(-2px);
  border-color: var(--header-bg);
}

.stat-card[style*="cursor: pointer"]:hover .stat-card-label,
.stat-card[style*="cursor: pointer"]:hover .stat-card-value,
.record-item[style*="cursor: pointer"]:hover .record-label,
.record-item[style*="cursor: pointer"]:hover .record-value {
  color: white;
}

/* Import/Export Section */
.stats-import-export-section {
  margin-top: 2rem;
  padding: 1.5rem;
  background-color: var(--card-bg);
  border-radius: 12px;
  border: 1px solid var(--border-color);
}

.import-export-buttons {
  display: flex;
  gap: 0.75rem;
  margin-top: 1rem;
  flex-wrap: wrap;
}

.import-export-btn {
  flex: 1;
  min-width: 140px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background-color: var(--accent-color);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease, transform 0.1s ease;
}

.import-export-btn:hover {
  background-color: var(--header-bg);
  transform: translateY(-1px);
}

.import-export-btn:active {
  transform: translateY(0);
}

.import-export-btn i {
  font-size: 1rem;
}

@media (max-width: 768px) {
  .import-export-buttons {
    flex-direction: column;
  }
}
