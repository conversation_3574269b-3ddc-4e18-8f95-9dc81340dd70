<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, user-scalable=no"
    />
    <meta name="format-detection" content="telephone=no" />

    <!-- SEO Meta Tags -->
    <title>scTimer - Speed Cubing Timer</title>
    <meta
      name="description"
      content="scTimer is a professional speed cubing timer with WCA inspection standards, statistics tracking, and support for all official WCA events. Perfect for speedcubers of all levels."
    />
    <meta
      name="keywords"
      content="speed cubing timer, speedcubing, rubik's cube timer, WCA timer, cube timer, 3x3 timer, speedcube, cubing statistics, inspection timer, scramble generator"
    />
    <meta name="author" content="scTimer" />
    <meta name="robots" content="index, follow" />
    <link rel="canonical" href="https://sctimer.com/" />

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="scTimer - Speed Cubing Timer" />
    <meta
      property="og:description"
      content="Professional speed cubing timer with WCA inspection standards, statistics tracking, and support for all official WCA events."
    />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://sctimer.com/" />
    <meta
      property="og:image"
      content="https://sctimer.com/assets/screenshots/desktop.png"
    />
    <meta
      property="og:image:alt"
      content="scTimer interface showing timer and statistics"
    />
    <meta property="og:site_name" content="scTimer" />
    <meta property="og:locale" content="en_US" />

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="scTimer - Speed Cubing Timer" />
    <meta
      name="twitter:description"
      content="Professional speed cubing timer with WCA inspection standards, statistics tracking, and support for all official WCA events."
    />
    <meta
      name="twitter:image"
      content="https://sctimer.com/assets/screenshots/desktop.png"
    />
    <meta
      name="twitter:image:alt"
      content="scTimer interface showing timer and statistics"
    />

    <!-- Favicon and Icons -->
    <link rel="shortcut icon" href="assets/favicon.ico" type="image/x-icon" />
    <link
      rel="icon"
      type="image/png"
      sizes="72x72"
      href="assets/icons/72x72.png"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="96x96"
      href="assets/icons/96x96.png"
    />

    <!-- Performance Optimization -->
    <link rel="preconnect" href="https://cdn.cubing.net" />
    <link rel="preconnect" href="https://cdnjs.cloudflare.com" />
    <link rel="dns-prefetch" href="https://cdn.cubing.net" />
    <link rel="dns-prefetch" href="https://cdnjs.cloudflare.com" />

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#a57865" />
    <link rel="manifest" href="manifest.json" />
    <!-- Mobile app support -->
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta
      name="apple-mobile-web-app-status-bar-style"
      content="black-translucent"
    />
    <meta name="apple-mobile-web-app-title" content="scTimer" />
    <link rel="apple-touch-icon" href="assets/icons/152x152.png" />
    <!-- iOS PWA specific tags -->
    <meta name="apple-touch-fullscreen" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />

    <!-- iOS splash screens -->
    <!-- iPhone X (1125px x 2436px) -->
    <link
      rel="apple-touch-startup-image"
      media="(device-width: 375px) and (device-height: 812px) and (-webkit-device-pixel-ratio: 3)"
      href="assets/icons/512x512.png"
    />
    <!-- iPhone 8, 7, 6s, 6 (750px x 1334px) -->
    <link
      rel="apple-touch-startup-image"
      media="(device-width: 375px) and (device-height: 667px) and (-webkit-device-pixel-ratio: 2)"
      href="assets/icons/512x512.png"
    />
    <!-- iPhone 8 Plus, 7 Plus, 6s Plus, 6 Plus (1242px x 2208px) -->
    <link
      rel="apple-touch-startup-image"
      media="(device-width: 414px) and (device-height: 736px) and (-webkit-device-pixel-ratio: 3)"
      href="assets/icons/512x512.png"
    />
    <!-- iPhone 5 (640px x 1136px) -->
    <link
      rel="apple-touch-startup-image"
      media="(device-width: 320px) and (device-height: 568px) and (-webkit-device-pixel-ratio: 2)"
      href="assets/icons/512x512.png"
    />
    <!-- iPad Pro 12.9" (2048px x 2732px) -->
    <link
      rel="apple-touch-startup-image"
      media="(device-width: 1024px) and (device-height: 1366px) and (-webkit-device-pixel-ratio: 2)"
      href="assets/icons/512x512.png"
    />
    <!-- iPad Pro 11" (1668px x 2388px) -->
    <link
      rel="apple-touch-startup-image"
      media="(device-width: 834px) and (device-height: 1194px) and (-webkit-device-pixel-ratio: 2)"
      href="assets/icons/512x512.png"
    />
    <!-- iPad Pro 10.5" (1668px x 2224px) -->
    <link
      rel="apple-touch-startup-image"
      media="(device-width: 834px) and (device-height: 1112px) and (-webkit-device-pixel-ratio: 2)"
      href="assets/icons/512x512.png"
    />
    <!-- iPad Mini, Air (1536px x 2048px) -->
    <link
      rel="apple-touch-startup-image"
      media="(device-width: 768px) and (device-height: 1024px) and (-webkit-device-pixel-ratio: 2)"
      href="assets/icons/512x512.png"
    />
    <!-- Cubing Icons -->
    <link
      rel="stylesheet"
      href="https://cdn.cubing.net/v0/css/@cubing/icons"
      crossorigin="anonymous"
    />
    <!-- Font Awesome for UI icons -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <!-- Lottie Animation Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lottie-web/5.12.2/lottie.min.js"></script>
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/fonts.css" />
    <link rel="stylesheet" href="assets/css/styles.css" />
    <link rel="stylesheet" href="assets/css/fmc-manager.css" />
    <link rel="stylesheet" href="assets/css/tutorial.css" />

    <!-- Structured Data (JSON-LD) -->
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "scTimer",
        "alternateName": "Speed Cubing Timer",
        "description": "Professional speed cubing timer with WCA inspection standards, statistics tracking, and support for all official WCA events. Perfect for speedcubers of all levels.",
        "url": "https://sctimer.com/",
        "applicationCategory": "GameApplication",
        "operatingSystem": "Web Browser",
        "offers": {
          "@type": "Offer",
          "price": "0",
          "priceCurrency": "USD"
        },
        "featureList": [
          "WCA Inspection Timer (15 seconds)",
          "Statistics Tracking (ao5, ao12, ao100, ao1000)",
          "Support for all WCA Events",
          "Scramble Generation",
          "Session Management",
          "Multi-language Support",
          "Stackmat Timer Support",
          "Progressive Web App",
          "Offline Functionality"
        ],
        "screenshot": "https://sctimer.com/assets/screenshots/desktop.png",
        "author": {
          "@type": "Organization",
          "name": "scTimer Team"
        },
        "keywords": "speed cubing, speedcubing, rubik's cube, timer, WCA, cube timer, 3x3, speedcube, cubing statistics, inspection timer, scramble generator",
        "inLanguage": ["en", "ar", "ckb"],
        "browserRequirements": "Requires JavaScript. Works on all modern browsers.",
        "softwareVersion": "1.0",
        "datePublished": "2024-01-01",
        "dateModified": "2025-01-01"
      }
    </script>

    <!-- Additional Structured Data for Sports/Gaming -->
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "SportsEvent",
        "name": "Speed Cubing Practice",
        "description": "Practice speed cubing with professional timer and WCA standards",
        "sport": "Speed Cubing",
        "eventStatus": "https://schema.org/EventScheduled",
        "eventAttendanceMode": "https://schema.org/OnlineEventAttendanceMode",
        "location": {
          "@type": "VirtualLocation",
          "url": "https://sctimer.com/"
        },
        "organizer": {
          "@type": "Organization",
          "name": "scTimer",
          "url": "https://sctimer.com/"
        }
      }
    </script>
  </head>
  <body>
    <!-- Times panel (slide-in from left) -->
    <div class="times-panel" id="times-panel">
      <div class="times-panel-header">
        <div class="times-panel-title" data-i18n="times.title">Solve Times</div>
        <button
          type="button"
          class="times-panel-close mobile-only"
          id="times-panel-close"
          title="Close"
        >
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="times-list">
        <ul id="times-list"></ul>
      </div>
      <div class="times-panel-footer">
        <div class="footer-buttons">
          <button type="button" id="clear-times-btn">
            <i class="fas fa-trash"></i>
            <span class="clear-times-text" data-i18n="times.clear"
              >Clear Times</span
            >
          </button>
          <button type="button" id="edit-session-btn" class="hidden">
            <i class="fas fa-edit"></i>
            <span class="edit-session-text" data-i18n="sessions.editSession"
              >Edit Session</span
            >
          </button>
          <button type="button" id="stats-btn" title="More Statistics">
            <i class="fas fa-bar-chart"></i>
            <span class="stats-text" data-i18n="stats.moreStats"
              >More Stats</span
            >
          </button>
        </div>
        <div class="footer-actions">
          <!-- Space for additional buttons if needed -->
        </div>
      </div>
    </div>

    <div class="container">
      <!-- Header with controls -->
      <header>
        <div class="header-left">
          <button
            type="button"
            id="learning-btn"
            class="learning-button"
            title="Algorithm Database"
          >
            <i class="fas fa-graduation-cap"></i>
          </button>
        </div>
        <div class="header-center">
          <div class="event-selector">
            <button
              type="button"
              class="event-selector-btn"
              id="event-selector-btn"
            >
              <span
                class="cubing-icon event-333"
                id="current-event-icon"
              ></span>
              <span id="current-event-text" data-i18n="events.333">3x3x3</span>
            </button>
            <div class="event-dropdown" id="event-dropdown">
              <div class="event-option" data-event="333">
                <span class="cubing-icon event-333"></span>
                <span data-i18n="events.333">3x3x3</span>
              </div>
              <div class="event-option" data-event="222">
                <span class="cubing-icon event-222"></span>
                <span data-i18n="events.222">2x2x2</span>
              </div>
              <div class="event-option" data-event="444">
                <span class="cubing-icon event-444"></span>
                <span data-i18n="events.444">4x4x4</span>
              </div>
              <div class="event-option" data-event="555">
                <span class="cubing-icon event-555"></span>
                <span data-i18n="events.555">5x5x5</span>
              </div>
              <div class="event-option" data-event="666">
                <span class="cubing-icon event-666"></span>
                <span data-i18n="events.666">6x6x6</span>
              </div>
              <div class="event-option" data-event="777">
                <span class="cubing-icon event-777"></span>
                <span data-i18n="events.777">7x7x7</span>
              </div>
              <div class="event-option" data-event="333bf">
                <span class="cubing-icon event-333bf"></span>
                <span data-i18n="events.333bf">3x3x3 Blindfolded</span>
              </div>
              <div class="event-option" data-event="333fm">
                <span class="cubing-icon event-333fm"></span>
                <span data-i18n="events.333fm">3x3x3 Fewest Moves</span>
              </div>
              <div class="event-option" data-event="333oh">
                <span class="cubing-icon event-333oh"></span>
                <span data-i18n="events.333oh">3x3x3 One-Handed</span>
              </div>
              <div class="event-option" data-event="clock">
                <span class="cubing-icon event-clock"></span>
                <span data-i18n="events.clock">Clock</span>
              </div>
              <div class="event-option" data-event="minx">
                <span class="cubing-icon event-minx"></span>
                <span data-i18n="events.minx">Megaminx</span>
              </div>
              <div class="event-option" data-event="pyram">
                <span class="cubing-icon event-pyram"></span>
                <span data-i18n="events.pyram">Pyraminx</span>
              </div>
              <div class="event-option" data-event="skewb">
                <span class="cubing-icon event-skewb"></span>
                <span data-i18n="events.skewb">Skewb</span>
              </div>
              <div class="event-option" data-event="sq1">
                <span class="cubing-icon event-sq1"></span>
                <span data-i18n="events.sq1">Square-1</span>
              </div>
              <div class="event-option" data-event="444bf">
                <span class="cubing-icon event-444bf"></span>
                <span data-i18n="events.444bf">4x4x4 Blindfolded</span>
              </div>
              <div class="event-option" data-event="555bf">
                <span class="cubing-icon event-555bf"></span>
                <span data-i18n="events.555bf">5x5x5 Blindfolded</span>
              </div>
              <div class="event-option" data-event="333mbf">
                <span class="cubing-icon event-333mbf"></span>
                <span data-i18n="events.333mbf">3x3x3 Multi-Blind</span>
              </div>
              <div class="event-divider"></div>
              <div
                class="event-option new-session-option"
                data-action="new-session"
              >
                <span class="fas fa-plus-circle"></span>
                <span data-i18n="sessions.newSession">New Session</span>
              </div>
            </div>
          </div>
          <button
            type="button"
            id="new-scramble"
            class="action-button"
            title="Next"
          >
            <i class="fas fa-random"></i>
          </button>
        </div>
        <div class="header-right">
          <button
            type="button"
            id="settings-btn"
            class="settings-button"
            title="Settings"
          >
            <i class="fas fa-cog"></i>
          </button>
        </div>
      </header>

      <!-- Main content area -->
      <div class="main-content">
        <!-- Scramble section -->
        <div class="scramble-section">
          <div class="scramble" id="scramble">
            <div class="scramble-loader" id="scramble-loader"></div>
            <span id="scramble-text" class="hidden"></span>
          </div>
        </div>

        <!-- Timer -->
        <div class="timer-container">
          <div class="timer" id="timer">0.000</div>
          <div class="inspection" id="inspection"></div>

          <!-- Stackmat Status -->
          <div class="stackmat-status" id="stackmat-status">
            <div class="stackmat-connection" id="stackmat-connection">
              <i class="fas fa-plug"></i>
              <span
                id="stackmat-connection-text"
                data-i18n="stackmat.disconnected"
                >Disconnected</span
              >
            </div>
          </div>

          <!-- Manual Input Timer -->
          <div class="manual-input-container" id="manual-input-container">
            <div class="manual-input-wrapper">
              <div class="input-container">
                <label for="manual-time-input" class="sr-only"
                  >Enter solve time manually</label
                >
                <input
                  type="text"
                  id="manual-time-input"
                  class="manual-time-input"
                  aria-label="Enter solve time manually"
                  placeholder="Enter time"
                />
                <div class="manual-inspection" id="manual-inspection"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- Stats -->
        <div class="stats-container" id="stats-container">
          <!-- Statistics title removed -->
          <div class="stats">
            <div class="stat-item">
              <div class="stat-label" data-i18n="stats.solves">Solves:</div>
              <div class="stat-value" id="solve-count">0</div>
            </div>
            <div class="stat-item">
              <div class="stat-label" data-i18n="stats.best">Best:</div>
              <div class="stat-value" id="best-time">-</div>
            </div>
            <div class="stat-item">
              <div class="stat-label" data-i18n="stats.mean">Mean:</div>
              <div class="stat-value" id="mean-time">-</div>
            </div>
            <div class="stat-item">
              <div class="stat-label" data-i18n="stats.avg5">ao5:</div>
              <div class="stat-value" id="avg5">-</div>
            </div>
            <div class="stat-item">
              <div class="stat-label" data-i18n="stats.avg12">ao12:</div>
              <div class="stat-value" id="avg12">-</div>
            </div>
            <div class="stat-item">
              <div class="stat-label" data-i18n="stats.avg100">ao100:</div>
              <div class="stat-value" id="avg100">-</div>
            </div>
          </div>
        </div>

        <!-- Visualization -->
        <div class="visualization" id="visualization-container">
          <!-- Twisty player will be inserted here -->
        </div>
      </div>

      <!-- SEO Content Section -->
      <section class="seo-content" style="display: none">
        <h1>scTimer - Speed Cubing Timer</h1>
        <p>
          scTimer is the ultimate speed cubing timer designed for speedcubers of
          all levels. Whether you're a beginner learning to solve the Rubik's
          cube or a competitive speedcuber aiming for world records, scTimer
          provides all the tools you need to track and improve your times.
        </p>

        <h2>Features</h2>
        <ul>
          <li>
            <strong>WCA Inspection Timer:</strong> Official 15-second inspection
            period following World Cube Association standards
          </li>
          <li>
            <strong>Comprehensive Statistics:</strong> Track your progress with
            ao5, ao12, ao100, and ao1000 averages
          </li>
          <li>
            <strong>All WCA Events:</strong> Support for 3x3x3, 2x2x2, 4x4x4,
            5x5x5, 6x6x6, 7x7x7, Pyraminx, Megaminx, Skewb, Square-1, Clock, and
            blindfolded events
          </li>
          <li>
            <strong>Scramble Generation:</strong> Random scrambles for all
            supported puzzle types
          </li>
          <li>
            <strong>Session Management:</strong> Organize your solves into
            different sessions
          </li>
          <li>
            <strong>Stackmat Support:</strong> Connect your Stackmat timer for
            official timing
          </li>
          <li>
            <strong>Multi-language Support:</strong> Available in English,
            Arabic, and Kurdish
          </li>
          <li>
            <strong>Progressive Web App:</strong> Install on your device and use
            offline
          </li>
        </ul>

        <h2>About Speed Cubing</h2>
        <p>
          Speed cubing, also known as speedcubing, is the hobby of solving
          Rubik's cubes and other twisty puzzles as quickly as possible. The
          World Cube Association (WCA) governs official competitions worldwide,
          setting standards for timing, scrambling, and judging.
        </p>

        <h2>Getting Started</h2>
        <p>
          To start using scTimer, simply press the spacebar to begin timing your
          solves. The timer includes a 15-second inspection period by default,
          following WCA regulations. Your times are automatically saved and
          statistics are calculated in real-time.
        </p>

        <h2>WCA Events Supported</h2>
        <ul>
          <li>3x3x3 Cube - The classic Rubik's cube</li>
          <li>2x2x2 Cube - Pocket cube for quick solves</li>
          <li>4x4x4 Cube - Rubik's Revenge with parity algorithms</li>
          <li>5x5x5 Cube - Professor's cube for endurance</li>
          <li>6x6x6 Cube - V-Cube 6 for advanced cubers</li>
          <li>7x7x7 Cube - V-Cube 7 for ultimate challenge</li>
          <li>3x3x3 One-Handed - Solve with one hand only</li>
          <li>3x3x3 Blindfolded - Memorize and solve without looking</li>
          <li>3x3x3 Fewest Moves - Solve in minimum moves</li>
          <li>Pyraminx - Tetrahedral puzzle</li>
          <li>Megaminx - Dodecahedral puzzle with 12 faces</li>
          <li>Skewb - Corner-turning cube puzzle</li>
          <li>Square-1 - Shape-shifting puzzle</li>
          <li>Clock - Time-based puzzle with gears</li>
        </ul>
      </section>

      <!-- Times panel moved outside container -->

      <!-- Settings modal -->
      <div class="settings-modal" id="settings-modal">
        <div class="settings-content">
          <div class="settings-header">
            <div class="settings-title" data-i18n="settings.title">
              Settings
            </div>
            <button
              type="button"
              class="settings-close"
              id="settings-close"
              title="Close"
            >
              <i class="fas fa-times"></i>
            </button>
          </div>
          <div class="settings-body">
            <div class="settings-section">
              <div class="settings-section-title">
                <i class="fas fa-stopwatch"></i>
                <span data-i18n="timerOptions.title">Timer Options</span>
              </div>

              <!-- Warning Sounds Option (only visible for FMC and MBLD) -->
              <div
                class="settings-option"
                id="warning-sounds-option"
                style="display: none"
              >
                <label class="checkbox-label">
                  <input type="checkbox" id="warning-sounds" checked />
                  <span data-i18n="timerOptions.warningSounds"
                    >Enable Warning Sounds</span
                  >
                </label>
              </div>

              <div class="settings-option" id="inspection-option">
                <label class="checkbox-label">
                  <input type="checkbox" id="use-inspection" checked />
                  <span data-i18n="timerOptions.useInspection"
                    >Use WCA Inspection (15s)</span
                  >
                </label>
              </div>
              <div class="settings-option" id="inspection-sound-option">
                <label class="dropdown-label">
                  <span data-i18n="timerOptions.inspectionSound"
                    >Inspection Sound:</span
                  >
                  <select
                    id="inspection-sound-selector"
                    class="inspection-sound-selector"
                  >
                    <option
                      value="none"
                      data-i18n="timerOptions.inspectionSoundNone"
                    >
                      None
                    </option>
                    <option
                      value="voice"
                      data-i18n="timerOptions.inspectionSoundVoice"
                    >
                      Voice
                    </option>
                    <option
                      value="beep"
                      data-i18n="timerOptions.inspectionSoundBeep"
                      selected
                    >
                      Beep
                    </option>
                  </select>
                </label>
              </div>
              <div class="settings-option" id="timer-mode-option">
                <label class="dropdown-label">
                  <span data-i18n="timerOptions.timerMode">Timer Mode:</span>
                  <select id="timer-mode-selector" class="timer-mode-selector">
                    <option
                      value="timer"
                      data-i18n="timerOptions.timerModeTimer"
                    >
                      Timer
                    </option>
                    <option
                      value="typing"
                      data-i18n="timerOptions.timerModeTyping"
                    >
                      Typing
                    </option>
                    <option
                      value="stackmat"
                      data-i18n="timerOptions.timerModeStackmat"
                    >
                      Stackmat
                    </option>
                    <option
                      value="bluetooth"
                      data-i18n="timerOptions.timerModeBluetooth"
                      disabled
                    >
                      Bluetooth (Coming Soon)
                    </option>
                  </select>
                </label>
              </div>

              <div
                class="settings-option stackmat-only-setting"
                style="display: none"
              >
                <label class="dropdown-label">
                  <span data-i18n="timerOptions.microphoneInput"
                    >Microphone Input:</span
                  >
                  <select id="microphone-selector" class="microphone-selector">
                    <option
                      value="auto"
                      data-i18n="timerOptions.microphoneAuto"
                    >
                      Auto-detect
                    </option>
                    <!-- Microphone options will be populated dynamically -->
                  </select>
                </label>
                <div class="setting-note">
                  <span data-i18n="timerOptions.microphoneNote"
                    >Choose your Y splitter or external microphone</span
                  >
                </div>
              </div>
              <div
                class="settings-option stackmat-only-setting"
                style="display: none"
              >
                <label class="checkbox-label">
                  <input
                    type="checkbox"
                    id="stackmat-reset-inspection"
                    checked
                  />
                  <span data-i18n="timerOptions.stackmatResetInspection"
                    >Stackmat Reset Triggers Inspection</span
                  >
                </label>
                <div class="setting-note">
                  <span data-i18n="timerOptions.stackmatResetNote"
                    >Note: Works after running the timer first</span
                  >
                </div>
              </div>
              <div class="settings-option">
                <label class="dropdown-label">
                  <span data-i18n="timerOptions.decimalPlaces"
                    >Decimal Places:</span
                  >
                  <select
                    id="decimal-places-selector"
                    class="decimal-places-selector"
                  >
                    <option
                      value="0"
                      data-i18n="timerOptions.decimalPlacesNone"
                    >
                      None (12)
                    </option>
                    <option value="1" data-i18n="timerOptions.decimalPlaces1">
                      1 (12.3)
                    </option>
                    <option value="2" data-i18n="timerOptions.decimalPlaces2">
                      2 (12.34)
                    </option>
                    <option
                      value="3"
                      data-i18n="timerOptions.decimalPlaces3"
                      selected
                    >
                      3 (12.345)
                    </option>
                  </select>
                </label>
              </div>
            </div>
            <div class="settings-section">
              <div class="settings-section-title">
                <i class="fas fa-eye"></i>
                <span data-i18n="displayOptions.title">Display Options</span>
              </div>
              <div class="settings-option">
                <label class="checkbox-label">
                  <input type="checkbox" id="show-visualization" checked />
                  <span data-i18n="displayOptions.showVisualization"
                    >Show Puzzle Visualization</span
                  >
                </label>
              </div>
              <div class="settings-option">
                <label class="checkbox-label">
                  <input type="checkbox" id="show-stats" checked />
                  <span data-i18n="displayOptions.showStats"
                    >Show Statistics</span
                  >
                </label>
              </div>

              <div class="settings-option">
                <label class="checkbox-label">
                  <input type="checkbox" id="dark-mode" checked />
                  <span data-i18n="displayOptions.darkMode">Dark Mode</span>
                </label>
              </div>

              <div class="settings-option slider-option">
                <label class="slider-label">
                  <span
                    class="scramble-font-size-text"
                    data-i18n="displayOptions.scrambleFontSize"
                    >Scramble Font Size</span
                  >
                  <div class="slider-container">
                    <input
                      type="range"
                      id="scramble-font-size"
                      min="0.1"
                      max="1.5"
                      step="0.1"
                      value="1.0"
                    />
                    <div class="slider-value" id="scramble-font-size-value">
                      1.0×
                    </div>
                  </div>
                </label>
              </div>
            </div>
            <div class="settings-section">
              <div class="settings-section-title">
                <i class="fas fa-globe"></i>
                <span data-i18n="settings.language">Language</span>
              </div>
              <div class="settings-option">
                <select
                  id="language-selector"
                  class="language-selector"
                  aria-label="Select language"
                >
                  <option value="en">English</option>
                  <option value="ckb">کوردی سۆرانی</option>
                  <option value="ku">Kurdî (Kurmancî)</option>
                  <option value="ar">العربية</option>
                  <option value="fa">فارسی</option>
                  <option value="tr">Türkçe</option>
                  <option value="es">Español</option>
                  <option value="fr">Français</option>
                  <option value="de">Deutsch</option>
                  <option value="zh-cn">中文 (简体)</option>
                  <option value="ja">日本語</option>
                  <option value="ko">한국어</option>
                  <option value="ru">Русский</option>
                  <option value="pt">Português</option>
                  <option value="it">Italiano</option>
                  <option value="nl">Nederlands</option>
                  <option value="pl">Polski</option>
                  <option value="sv">Svenska</option>
                  <option value="no">Norsk</option>
                  <option value="fi">Suomi</option>
                  <option value="hu">Magyar</option>
                </select>
              </div>
            </div>
            <div class="settings-section">
              <div class="settings-section-title sync-section-title">
                <i class="fab fa-google-drive"></i>
                <span data-i18n="sync.title">Google Drive Sync</span>
              </div>

              <!-- Connection Status and Actions -->
              <div class="settings-option">
                <div class="sync-status-container">
                  <div class="sync-status-info">
                    <span class="sync-status-label" data-i18n="sync.status"
                      >Status:</span
                    >
                    <span
                      class="sync-status"
                      id="sync-status"
                      data-i18n="sync.notConnected"
                    >
                      Not connected
                    </span>
                  </div>
                  <div class="sync-connection-buttons">
                    <button
                      type="button"
                      id="sync-connect-btn"
                      class="action-button sync-connect-btn"
                      data-i18n="sync.connect"
                    >
                      <i class="fas fa-link"></i>
                      <span>Connect</span>
                    </button>
                    <button
                      type="button"
                      id="sync-disconnect-btn"
                      class="action-button sync-disconnect-btn"
                      style="display: none"
                      data-i18n="sync.disconnect"
                    >
                      <i class="fas fa-unlink"></i>
                      <span>Disconnect</span>
                    </button>
                  </div>
                </div>

                <!-- Sync Actions next to status -->
                <div
                  class="sync-actions"
                  id="sync-actions"
                  style="display: none"
                >
                  <button
                    type="button"
                    id="sync-upload-btn"
                    class="action-button sync-action-btn"
                    data-i18n="sync.upload"
                  >
                    <i class="fas fa-cloud-upload-alt"></i>
                    <span>Upload to Drive</span>
                  </button>
                  <button
                    type="button"
                    id="sync-download-btn"
                    class="action-button sync-action-btn"
                    data-i18n="sync.download"
                  >
                    <i class="fas fa-cloud-download-alt"></i>
                    <span>Download from Drive</span>
                  </button>
                </div>
              </div>

              <!-- Auto Sync Toggle -->
              <div
                class="settings-option sync-auto-option"
                id="sync-auto-option"
                style="text-align: left; display: none"
              >
                <label class="checkbox-label">
                  <input type="checkbox" id="sync-auto-checkbox" />
                  <span data-i18n="sync.autoSync">Auto Sync</span>
                </label>
                <span class="setting-note" data-i18n="sync.autoSyncNote">
                  Automatically sync your times when connected to the internet
                </span>
              </div>
            </div>
          </div>
          <div class="settings-footer">
            <button
              type="button"
              id="restart-tutorial-btn"
              class="action-button tutorial-btn-secondary"
            >
              <i class="fas fa-route"></i>
              <span data-i18n="tutorial.restartTutorial">App Tour</span>
            </button>

            <button type="button" id="settings-save" class="action-button">
              <i class="fas fa-check"></i>
              <span data-i18n="settings.save">Save</span>
            </button>

            <button
              type="button"
              id="info-btn"
              class="action-button info-btn-secondary"
            >
              <i class="fas fa-info-circle"></i>
              <span data-i18n="info.title">Info</span>
            </button>
          </div>
        </div>
      </div>

      <!-- Info Modal -->
      <div class="info-modal" id="info-modal">
        <div class="info-modal-content">
          <div class="info-modal-header">
            <div class="info-modal-title" data-i18n="info.title">Info</div>
            <button
              type="button"
              class="info-modal-close"
              id="info-modal-close"
              title="Close"
            >
              <i class="fas fa-times"></i>
            </button>
          </div>
          <div class="info-modal-body">
            <!-- PWA Install Section -->
            <div class="info-section">
              <div class="info-section-title" data-i18n="info.pwaInstall.title">
                <i class="fas fa-mobile-alt"></i>
                Install as App
              </div>
              <div class="info-section-content">
                <p data-i18n="info.pwaInstall.description">
                  Install scTimer as a Progressive Web App for the best
                  experience. Works offline and feels like a native app.
                </p>
                <button
                  type="button"
                  id="pwa-install-btn"
                  class="info-action-btn"
                  style="display: none"
                >
                  <i class="fas fa-download"></i>
                  <span data-i18n="info.pwaInstall.install">Install App</span>
                </button>
                <div class="info-note" data-i18n="info.pwaInstall.note">
                  Available on Chrome, Safari, and other modern browsers
                </div>
              </div>
            </div>

            <!-- Keyboard Shortcuts Section -->
            <div class="info-section">
              <div class="info-section-title" data-i18n="info.shortcuts.title">
                <i class="fas fa-keyboard"></i>
                Keyboard Shortcuts
              </div>
              <div class="info-section-content">
                <div class="shortcuts-grid">
                  <div class="shortcut-category">
                    <h4 data-i18n="info.shortcuts.timer">Timer Controls</h4>
                    <div class="shortcut-item">
                      <kbd>SPACEBAR</kbd>
                      <span data-i18n="info.shortcuts.spacebar"
                        >Start/stop timer</span
                      >
                    </div>
                    <div class="shortcut-item">
                      <kbd>ESC</kbd>
                      <span data-i18n="info.shortcuts.escape"
                        >Cancel inspection & close modals</span
                      >
                    </div>
                  </div>

                  <div class="shortcut-category">
                    <h4 data-i18n="info.shortcuts.navigation">
                      Navigation & Actions
                    </h4>
                    <div class="shortcut-item">
                      <kbd>G</kbd>
                      <span data-i18n="info.shortcuts.generate"
                        >Generate new scramble</span
                      >
                    </div>
                    <div class="shortcut-item">
                      <kbd>L</kbd>
                      <span data-i18n="info.shortcuts.list"
                        >Toggle solve times list</span
                      >
                    </div>
                    <div class="shortcut-item">
                      <kbd>S</kbd>
                      <span data-i18n="info.shortcuts.settings"
                        >Open settings</span
                      >
                    </div>
                    <div class="shortcut-item">
                      <kbd>E</kbd>
                      <span data-i18n="info.shortcuts.edit"
                        >Edit current scramble</span
                      >
                    </div>
                    <div class="shortcut-item">
                      <kbd>C</kbd>
                      <span data-i18n="info.shortcuts.copy"
                        >Copy scramble to clipboard</span
                      >
                    </div>
                    <div class="shortcut-item">
                      <kbd>A</kbd>
                      <span data-i18n="info.shortcuts.stats"
                        >Open detailed statistics</span
                      >
                    </div>
                  </div>

                  <div class="shortcut-category">
                    <h4 data-i18n="info.shortcuts.display">Display Toggles</h4>
                    <div class="shortcut-item">
                      <kbd>V</kbd>
                      <span data-i18n="info.shortcuts.visualization"
                        >Toggle puzzle visualization</span
                      >
                    </div>
                    <div class="shortcut-item">
                      <kbd>B</kbd>
                      <span data-i18n="info.shortcuts.statistics"
                        >Toggle statistics display</span
                      >
                    </div>
                    <div class="shortcut-item">
                      <kbd>D</kbd>
                      <span data-i18n="info.shortcuts.darkMode"
                        >Toggle dark mode</span
                      >
                    </div>
                    <div class="shortcut-item">
                      <kbd>I</kbd>
                      <span data-i18n="info.shortcuts.inspection"
                        >Toggle WCA inspection</span
                      >
                    </div>
                  </div>

                  <div class="shortcut-category">
                    <h4 data-i18n="info.shortcuts.penalties">
                      Penalty Management
                    </h4>
                    <div class="shortcut-item">
                      <kbd>CTRL+1</kbd>
                      <span data-i18n="info.shortcuts.removePenalty"
                        >Remove penalty from recent solve</span
                      >
                    </div>
                    <div class="shortcut-item">
                      <kbd>CTRL+2</kbd>
                      <span data-i18n="info.shortcuts.addPlus2"
                        >Add +2 penalty to recent solve</span
                      >
                    </div>
                    <div class="shortcut-item">
                      <kbd>CTRL+3</kbd>
                      <span data-i18n="info.shortcuts.addDNF"
                        >Add DNF penalty to recent solve</span
                      >
                    </div>
                  </div>

                  <div class="shortcut-category">
                    <h4 data-i18n="info.shortcuts.session">
                      Session Management
                    </h4>
                    <div class="shortcut-item">
                      <kbd>CTRL+Q</kbd>
                      <span data-i18n="info.shortcuts.emptySession"
                        >Empty current session</span
                      >
                    </div>
                    <div class="shortcut-item">
                      <kbd>CTRL+X</kbd>
                      <span data-i18n="info.shortcuts.exportSession"
                        >Export current session</span
                      >
                    </div>
                    <div class="shortcut-item">
                      <kbd>ALT+N</kbd>
                      <span data-i18n="info.shortcuts.altN"
                        >Create new session</span
                      >
                    </div>
                  </div>

                  <div class="shortcut-category">
                    <h4 data-i18n="info.shortcuts.eventSwitching">
                      Event Switching
                    </h4>
                    <div class="shortcut-item">
                      <kbd>ALT+2-7</kbd>
                      <span data-i18n="info.shortcuts.alt2to7"
                        >Switch to 2×2×2 through 7×7×7 cubes</span
                      >
                    </div>
                    <div class="shortcut-item">
                      <kbd>ALT+P</kbd>
                      <span data-i18n="info.shortcuts.altP"
                        >Switch to Pyraminx</span
                      >
                    </div>
                    <div class="shortcut-item">
                      <kbd>ALT+M</kbd>
                      <span data-i18n="info.shortcuts.altM"
                        >Switch to Megaminx</span
                      >
                    </div>
                    <div class="shortcut-item">
                      <kbd>ALT+C</kbd>
                      <span data-i18n="info.shortcuts.altC"
                        >Switch to Clock</span
                      >
                    </div>
                    <div class="shortcut-item">
                      <kbd>ALT+S</kbd>
                      <span data-i18n="info.shortcuts.altS"
                        >Switch to Skewb</span
                      >
                    </div>
                    <div class="shortcut-item">
                      <kbd>ALT+1</kbd>
                      <span data-i18n="info.shortcuts.alt1"
                        >Switch to Square-1</span
                      >
                    </div>
                    <div class="shortcut-item">
                      <kbd>ALT+F</kbd>
                      <span data-i18n="info.shortcuts.altF"
                        >Switch to 3×3×3 Fewest Moves</span
                      >
                    </div>
                    <div class="shortcut-item">
                      <kbd>ALT+O</kbd>
                      <span data-i18n="info.shortcuts.altO"
                        >Switch to 3×3×3 One-Handed</span
                      >
                    </div>
                  </div>

                  <div class="shortcut-category">
                    <h4 data-i18n="info.shortcuts.blindfolded">
                      Blindfolded Events
                    </h4>
                    <div class="shortcut-item">
                      <kbd>ALT+CTRL+3</kbd>
                      <span data-i18n="info.shortcuts.altCtrl3"
                        >Switch to 3×3×3 Blindfolded</span
                      >
                    </div>
                    <div class="shortcut-item">
                      <kbd>ALT+CTRL+4</kbd>
                      <span data-i18n="info.shortcuts.altCtrl4"
                        >Switch to 4×4×4 Blindfolded</span
                      >
                    </div>
                    <div class="shortcut-item">
                      <kbd>ALT+CTRL+5</kbd>
                      <span data-i18n="info.shortcuts.altCtrl5"
                        >Switch to 5×5×5 Blindfolded</span
                      >
                    </div>
                    <div class="shortcut-item">
                      <kbd>ALT+CTRL+6</kbd>
                      <span data-i18n="info.shortcuts.altCtrl6"
                        >Switch to 3×3×3 Multi-Blind</span
                      >
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Mobile Gestures Section -->
            <div class="info-section">
              <div class="info-section-title" data-i18n="info.gestures.title">
                <i class="fas fa-hand-pointer"></i>
                Mobile Gestures
              </div>
              <div class="info-section-content">
                <div class="gesture-item">
                  <div class="gesture-icon">
                    <i class="fas fa-arrow-down"></i>
                  </div>
                  <div class="gesture-description">
                    <strong data-i18n="info.gestures.swipeDown"
                      >Swipe Down</strong
                    >
                    <span data-i18n="info.gestures.swipeDownDesc"
                      >Delete recent solve</span
                    >
                  </div>
                </div>
                <div class="gesture-item">
                  <div class="gesture-icon">
                    <i class="fas fa-arrow-up"></i>
                  </div>
                  <div class="gesture-description">
                    <strong data-i18n="info.gestures.swipeUp">Swipe Up</strong>
                    <span data-i18n="info.gestures.swipeUpDesc"
                      >Cycle through penalties (none/+2/DNF)</span
                    >
                  </div>
                </div>
                <div class="gesture-item">
                  <div class="gesture-icon">
                    <i class="fas fa-arrow-left"></i>
                  </div>
                  <div class="gesture-description">
                    <strong data-i18n="info.gestures.swipeLeft"
                      >Swipe Left</strong
                    >
                    <span data-i18n="info.gestures.swipeLeftDesc"
                      >LTR: New scramble | RTL: Times list</span
                    >
                  </div>
                </div>
                <div class="gesture-item">
                  <div class="gesture-icon">
                    <i class="fas fa-arrow-right"></i>
                  </div>
                  <div class="gesture-description">
                    <strong data-i18n="info.gestures.swipeRight"
                      >Swipe Right</strong
                    >
                    <span data-i18n="info.gestures.swipeRightDesc"
                      >LTR: Times list | RTL: New scramble</span
                    >
                  </div>
                </div>
                <div class="gesture-item">
                  <div class="gesture-icon">
                    <i class="fas fa-mouse-pointer"></i>
                  </div>
                  <div class="gesture-description">
                    <strong data-i18n="info.gestures.doubleClick"
                      >Double Click</strong
                    >
                    <span data-i18n="info.gestures.doubleClickDesc"
                      >Copy current scramble (PC/Mobile)</span
                    >
                  </div>
                </div>
                <div class="gesture-item">
                  <div class="gesture-icon">
                    <i class="fas fa-hand-paper"></i>
                  </div>
                  <div class="gesture-description">
                    <strong data-i18n="info.gestures.longPress"
                      >Long Press/Click & Hold</strong
                    >
                    <span data-i18n="info.gestures.longPressDesc"
                      >Edit current scramble (PC/Mobile)</span
                    >
                  </div>
                </div>
              </div>
            </div>

            <!-- Features Section -->
            <div class="info-section">
              <div class="info-section-title" data-i18n="info.features.title">
                <i class="fas fa-star"></i>
                Key Features
              </div>
              <div class="info-section-content">
                <div class="features-grid">
                  <div class="feature-item">
                    <i class="fas fa-stopwatch"></i>
                    <div>
                      <strong data-i18n="info.features.timer"
                        >Professional Timer</strong
                      >
                      <p data-i18n="info.features.timerDesc">
                        WCA-compliant timing with inspection mode
                      </p>
                    </div>
                  </div>
                  <div class="feature-item">
                    <i class="fas fa-cubes"></i>
                    <div>
                      <strong data-i18n="info.features.puzzles"
                        >All WCA Events</strong
                      >
                      <p data-i18n="info.features.puzzlesDesc">
                        Complete support for all official WCA puzzle events
                      </p>
                    </div>
                  </div>
                  <div class="feature-item">
                    <i class="fas fa-chart-line"></i>
                    <div>
                      <strong data-i18n="info.features.statistics"
                        >Advanced Statistics</strong
                      >
                      <p data-i18n="info.features.statisticsDesc">
                        Detailed analytics with ao5, ao12, ao100
                      </p>
                    </div>
                  </div>
                  <div class="feature-item">
                    <i class="fas fa-random"></i>
                    <div>
                      <strong data-i18n="info.features.scrambles"
                        >Official Scrambles</strong
                      >
                      <p data-i18n="info.features.scramblesDesc">
                        WCA-standard scramble generation with 2D visualization
                      </p>
                    </div>
                  </div>
                  <div class="feature-item">
                    <i class="fas fa-globe"></i>
                    <div>
                      <strong data-i18n="info.features.multilingual"
                        >Multilingual Support</strong
                      >
                      <p data-i18n="info.features.multilingualDesc">
                        15+ languages with RTL support
                      </p>
                    </div>
                  </div>
                  <div class="feature-item">
                    <i class="fas fa-cloud-upload-alt"></i>
                    <div>
                      <strong data-i18n="info.features.sync"
                        >Google Drive Sync</strong
                      >
                      <p data-i18n="info.features.syncDesc">
                        Cross-device synchronization with smart merging
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Google Drive Sync Section -->
            <div class="info-section">
              <div class="info-section-title" data-i18n="info.sync.title">
                <i class="fab fa-google-drive"></i>
                Google Drive Sync
              </div>
              <div class="info-section-content">
                <p data-i18n="info.sync.description">
                  Synchronize your solve times across all devices using Google
                  Drive. Your data is securely stored in your personal Google
                  Drive account.
                </p>
                <div class="sync-features">
                  <div class="sync-feature">
                    <i class="fas fa-shield-alt"></i>
                    <span data-i18n="info.sync.secure">Secure & Private</span>
                  </div>
                  <div class="sync-feature">
                    <i class="fas fa-sync-alt"></i>
                    <span data-i18n="info.sync.automatic">Automatic Sync</span>
                  </div>
                  <div class="sync-feature">
                    <i class="fas fa-wifi-slash"></i>
                    <span data-i18n="info.sync.offline">Offline Support</span>
                  </div>
                  <div class="sync-feature">
                    <i class="fas fa-code-branch"></i>
                    <span data-i18n="info.sync.smartMerge">Smart Merging</span>
                  </div>
                </div>
                <div class="info-note" data-i18n="info.sync.note">
                  Enable Google Drive sync in Settings to keep your times
                  synchronized across all your devices.
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Debug info -->
      <div class="debug-info" id="debug-info">
        <div>
          <span data-i18n="debug.timerState">Timer State:</span>
          <span id="timer-state">IDLE</span>
        </div>
        <div>
          <span data-i18n="debug.spaceHeldFor">Space Held For:</span>
          <span id="space-held-time">0</span>ms
        </div>
        <div>
          <span data-i18n="debug.currentEvent">Current Event:</span>
          <span id="current-event">333</span>
        </div>
        <div>
          <span data-i18n="debug.scrambleSource">Scramble Source:</span>
          <span id="scramble-source">-</span>
        </div>
      </div>

      <!-- Solve Details Modal -->
      <div class="solve-details-modal" id="solve-details-modal">
        <div class="solve-details-content">
          <div class="solve-details-header">
            <div class="solve-details-title" data-i18n="solveDetails.title">
              Solve Details
            </div>
            <button
              type="button"
              class="solve-details-close"
              id="solve-details-close"
              title="Close"
            >
              <i class="fas fa-times"></i>
            </button>
          </div>
          <div class="solve-details-body">
            <div class="solve-details-time">
              <span id="solve-details-time-value">0.000</span>
            </div>
            <div class="solve-details-date">
              <span id="solve-details-date-value">Date</span>
            </div>
            <div class="solve-details-section">
              <div class="solve-details-section-header">
                <div
                  class="solve-details-section-title"
                  data-i18n="solveDetails.scramble"
                >
                  Scramble
                </div>
                <button
                  type="button"
                  class="copy-scramble-btn"
                  id="copy-scramble-btn"
                  title="Copy scramble"
                  data-i18n-title="solveDetails.copyScramble"
                >
                  <i class="fas fa-copy"></i>
                </button>
              </div>
              <div
                class="solve-details-scramble"
                id="solve-details-scramble"
              ></div>
            </div>
            <div
              class="solve-details-section"
              id="solve-details-solution-section"
              style="display: none"
            >
              <div class="solve-details-section-header">
                <div
                  class="solve-details-section-title"
                  data-i18n="solveDetails.solution"
                >
                  Solution
                </div>
                <button
                  type="button"
                  class="copy-scramble-btn"
                  id="copy-solution-btn"
                  title="Copy solution"
                  data-i18n-title="solveDetails.copySolution"
                >
                  <i class="fas fa-copy"></i>
                </button>
              </div>
              <div
                class="solve-details-scramble"
                id="solve-details-solution"
              ></div>
            </div>
            <div class="solve-details-section">
              <div
                class="solve-details-section-title"
                data-i18n="solveDetails.penalty"
              >
                Penalty
              </div>
              <div class="solve-details-penalty">
                <label class="radio-label">
                  <input type="radio" name="penalty" value="none" checked />
                  <span data-i18n="solveDetails.none">None</span>
                </label>
                <label class="radio-label">
                  <input type="radio" name="penalty" value="+2" />
                  <span data-i18n="solveDetails.plusTwo">+2</span>
                </label>
                <label class="radio-label">
                  <input type="radio" name="penalty" value="dnf" />
                  <span data-i18n="solveDetails.dnf">DNF</span>
                </label>
              </div>
            </div>
            <div class="solve-details-section">
              <div
                class="solve-details-section-title"
                data-i18n="solveDetails.comment"
              >
                Comment
              </div>
              <textarea
                id="solve-details-comment"
                placeholder="Add a comment..."
                data-i18n-placeholder="solveDetails.addComment"
              ></textarea>
            </div>
          </div>
          <div class="solve-details-footer">
            <button
              type="button"
              id="solve-details-share"
              class="action-button"
            >
              <i class="fas fa-share-alt"></i>
              <span data-i18n="solveDetails.share">Share</span>
            </button>
            <button type="button" id="solve-details-save" class="action-button">
              <i class="fas fa-check"></i>
              <span data-i18n="solveDetails.save">Save</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- MBLD Cube Count Modal -->
    <div class="mbld-modal" id="mbld-cube-count-modal">
      <div class="mbld-modal-content">
        <div class="mbld-modal-header">
          <div class="mbld-modal-title" data-i18n="mbld.setup">
            Multi-Blind Setup
          </div>
          <button
            type="button"
            class="mbld-modal-close"
            id="mbld-cube-count-close"
            title="Close"
          >
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="mbld-modal-body">
          <div class="mbld-input-group">
            <label for="mbld-cube-count" data-i18n="mbld.numberOfCubesMinimum"
              >Number of cubes (minimum 2):</label
            >
            <input
              type="number"
              id="mbld-cube-count"
              min="2"
              value="2"
              class="mbld-input"
            />
          </div>
        </div>
        <div class="mbld-modal-footer">
          <button type="button" id="mbld-cube-count-save" class="action-button">
            <i class="fas fa-check"></i>
            <span data-i18n="mbld.generateScrambles">Generate Scrambles</span>
          </button>
        </div>
      </div>
    </div>

    <!-- MBLD Scrambles Modal -->
    <div class="mbld-modal" id="mbld-scrambles-modal">
      <div class="mbld-modal-content mbld-scrambles-content">
        <div class="mbld-modal-header">
          <div class="mbld-modal-title" data-i18n="mbld.scrambles">
            Multi-Blind Scrambles
          </div>
          <button
            type="button"
            class="mbld-modal-close"
            id="mbld-scrambles-close"
            title="Close"
          >
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="mbld-modal-body" id="mbld-scrambles-container">
          <!-- Scrambles will be inserted here -->
        </div>
      </div>
    </div>

    <!-- MBLD Visualizations Modal -->
    <div class="mbld-modal" id="mbld-visualizations-modal">
      <div class="mbld-modal-content mbld-visualizations-content">
        <div class="mbld-modal-header">
          <div class="mbld-modal-title" data-i18n="mbld.visualizations">
            Multi-Blind Visualizations
          </div>
          <button
            type="button"
            class="mbld-modal-close"
            id="mbld-visualizations-close"
            title="Close"
          >
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="mbld-modal-body" id="mbld-visualizations-container">
          <!-- Visualizations will be inserted here -->
        </div>
      </div>
    </div>

    <!-- MBLD Results Modal -->
    <div class="mbld-modal" id="mbld-results-modal">
      <div class="mbld-modal-content">
        <div class="mbld-modal-header">
          <div class="mbld-modal-title" data-i18n="mbld.results">
            Multi-Blind Results
          </div>
          <button
            type="button"
            class="mbld-modal-close"
            id="mbld-results-close"
            title="Close"
          >
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="mbld-modal-body">
          <div id="mbld-results-info" class="mbld-info"></div>
          <div class="mbld-input-group">
            <label for="mbld-solved-count" data-i18n="mbld.numberOfCubesSolved"
              >Number of cubes solved:</label
            >
            <input
              type="number"
              id="mbld-solved-count"
              min="0"
              value="0"
              class="mbld-input"
            />
          </div>
          <div class="mbld-input-group">
            <label for="mbld-total-count" data-i18n="app.outOf">Out of:</label>
            <input
              type="number"
              id="mbld-total-count"
              min="2"
              value="2"
              class="mbld-input"
              readonly
            />
          </div>
        </div>
        <div class="mbld-modal-footer">
          <button type="button" id="mbld-results-save" class="action-button">
            <i class="fas fa-check"></i>
            <span data-i18n="mbld.saveResult">Save Result</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Tutorial Welcome Modal -->
    <div class="tutorial-welcome-modal" id="tutorial-welcome-modal">
      <div class="tutorial-welcome-content">
        <div class="tutorial-welcome-header">
          <div class="tutorial-welcome-icon">
            <i class="fas fa-graduation-cap"></i>
          </div>
          <h2 class="tutorial-welcome-title" data-i18n="tutorial.welcomeTitle">
            Welcome to scTimer!
          </h2>
          <p
            class="tutorial-welcome-subtitle"
            data-i18n="tutorial.welcomeSubtitle"
          >
            Your professional speed cubing timer
          </p>
        </div>
        <div class="tutorial-welcome-body">
          <div class="tutorial-language-selector">
            <label
              for="tutorial-language-select"
              class="tutorial-language-label"
            >
              <i class="fas fa-globe"></i>
              <span data-i18n="tutorial.selectLanguage">Select Language:</span>
            </label>
            <select
              id="tutorial-language-select"
              class="tutorial-language-select"
            >
              <option value="en">English</option>
              <option value="ckb">کوردی سۆرانی</option>
              <option value="ku">Kurdî (Kurmancî)</option>
              <option value="ar">العربية</option>
              <option value="fa">فارسی</option>
              <option value="tr">Türkçe</option>
              <option value="es">Español</option>
              <option value="fr">Français</option>
              <option value="de">Deutsch</option>
              <option value="zh-cn">中文 (简体)</option>
              <option value="ja">日本語</option>
              <option value="ko">한국어</option>
              <option value="ru">Русский</option>
              <option value="pt">Português</option>
              <option value="it">Italiano</option>
              <option value="nl">Nederlands</option>
              <option value="pl">Polski</option>
              <option value="sv">Svenska</option>
              <option value="no">Norsk</option>
              <option value="fi">Suomi</option>
              <option value="hu">Magyar</option>
            </select>
          </div>
          <div class="tutorial-welcome-features">
            <div class="tutorial-feature">
              <i class="fas fa-stopwatch"></i>
              <span data-i18n="tutorial.feature1">WCA Standard Timer</span>
            </div>
            <div class="tutorial-feature">
              <i class="fas fa-chart-line"></i>
              <span data-i18n="tutorial.feature2">Advanced Statistics</span>
            </div>
            <div class="tutorial-feature">
              <i class="fas fa-cubes"></i>
              <span data-i18n="tutorial.feature3">All WCA Events</span>
            </div>
            <div class="tutorial-feature">
              <i class="fas fa-random"></i>
              <span data-i18n="tutorial.feature4">Scramble Generator</span>
            </div>
          </div>
          <p
            class="tutorial-welcome-description"
            data-i18n="tutorial.welcomeDescription"
          >
            Would you like a quick tour to learn how to use scTimer effectively?
            The tutorial will guide you through the main features in just a few
            steps.
          </p>
        </div>
        <div class="tutorial-welcome-footer">
          <button
            type="button"
            class="tutorial-btn tutorial-btn-secondary"
            id="tutorial-skip-btn"
          >
            <i class="fas fa-times"></i>
            <span data-i18n="tutorial.skipTutorial">Skip Tutorial</span>
          </button>
          <button
            type="button"
            class="tutorial-btn tutorial-btn-primary"
            id="tutorial-start-btn"
          >
            <i class="fas fa-play"></i>
            <span data-i18n="tutorial.startTour">Start Tour</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Tutorial Overlay -->
    <div class="tutorial-overlay" id="tutorial-overlay">
      <div class="tutorial-spotlight" id="tutorial-spotlight"></div>
      <div class="tutorial-tooltip" id="tutorial-tooltip">
        <div class="tutorial-tooltip-header">
          <div class="tutorial-step-counter">
            <span id="tutorial-current-step">1</span> of
            <span id="tutorial-total-steps">9</span>
          </div>
          <button
            type="button"
            class="tutorial-close-btn"
            id="tutorial-close-btn"
          >
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="tutorial-tooltip-content">
          <h3 class="tutorial-tooltip-title" id="tutorial-tooltip-title">
            Welcome!
          </h3>
          <p class="tutorial-tooltip-text" id="tutorial-tooltip-text">
            Let's start your journey with scTimer.
          </p>
        </div>
        <div class="tutorial-tooltip-footer">
          <button
            type="button"
            class="tutorial-btn tutorial-btn-secondary"
            id="tutorial-prev-btn"
            style="display: none"
          >
            <i class="fas fa-arrow-left"></i>
            Previous
          </button>
          <button
            type="button"
            class="tutorial-btn tutorial-btn-primary"
            id="tutorial-next-btn"
          >
            Next
            <i class="fas fa-arrow-right"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- Learning/Tutorial Modal -->
    <div class="learning-modal" id="learning-modal">
      <div class="learning-modal-header">
        <div class="learning-modal-title-container">
          <span class="learning-modal-title-text">Algorithm Database</span>
          <div class="learning-selectors">
            <div class="learning-puzzle-selector">
              <button
                type="button"
                class="learning-puzzle-selector-btn"
                id="learning-puzzle-selector-btn"
                title="Select Puzzle"
              >
                <span class="learning-puzzle-text" id="learning-puzzle-text"
                  >3x3x3 Cube</span
                >
                <i class="fas fa-chevron-down"></i>
              </button>
              <div
                class="learning-puzzle-dropdown"
                id="learning-puzzle-dropdown"
              >
                <div class="learning-puzzle-option" data-puzzle="3x3x3 Cube">
                  3x3x3 Cube
                </div>
              </div>
            </div>
            <div class="learning-method-selector">
              <button
                type="button"
                class="learning-method-selector-btn"
                id="learning-method-selector-btn"
                title="Select Method"
              >
                <span class="learning-method-text" id="learning-method-text"
                  >CFOP</span
                >
                <i class="fas fa-chevron-down"></i>
              </button>
              <div
                class="learning-method-dropdown"
                id="learning-method-dropdown"
              >
                <div class="learning-method-option" data-method="CFOP">
                  CFOP
                </div>
              </div>
            </div>
          </div>
        </div>
        <button
          type="button"
          class="learning-modal-close"
          id="learning-modal-close"
          title="Close Learning Modal"
        >
          <i class="fas fa-times"></i>
        </button>
      </div>

      <div class="learning-modal-content">
        <div class="learning-tabs-container">
          <div class="learning-tabs" id="learning-tabs">
            <button type="button" class="learning-tab active" data-algset="F2L">
              F2L
            </button>
            <button type="button" class="learning-tab" data-algset="OLL">
              OLL
            </button>
            <button type="button" class="learning-tab" data-algset="PLL">
              PLL
            </button>
          </div>
        </div>

        <div class="learning-content-area">
          <div class="learning-algset-info" id="learning-algset-info">
            <h3 id="learning-algset-name">First Two Layers</h3>
            <p id="learning-algset-description">
              Algorithms for solving the first two layers simultaneously
            </p>
          </div>

          <div class="learning-algorithms-grid" id="learning-algorithms-grid">
            <!-- Algorithm cards will be populated here -->
          </div>
        </div>
      </div>
    </div>

    <!-- New Session Modal -->
    <div class="modal" id="new-session-modal">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-title" data-i18n="sessions.newSessionTitle">
            New Session
          </div>
          <button
            type="button"
            class="modal-close"
            id="new-session-close"
            title="Close"
          >
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="input-group">
            <label for="session-name" data-i18n="sessions.sessionName"
              >Session Name:</label
            >
            <input
              type="text"
              id="session-name"
              class="session-input"
              placeholder="My Session"
              data-i18n-placeholder="sessions.sessionNamePlaceholder"
            />
          </div>
          <div class="input-group">
            <label for="session-puzzle" data-i18n="sessions.puzzleType"
              >Puzzle Type:</label
            >
            <select id="session-puzzle" class="session-input">
              <optgroup label="WCA Puzzles">
                <option value="222" data-i18n="events.222">2x2x2</option>
                <option value="333" data-i18n="events.333" selected>
                  3x3x3
                </option>
                <option value="444" data-i18n="events.444">4x4x4</option>
                <option value="555" data-i18n="events.555">5x5x5</option>
                <option value="666" data-i18n="events.666">6x6x6</option>
                <option value="777" data-i18n="events.777">7x7x7</option>
                <option value="333oh" data-i18n="events.333oh">
                  3x3x3 One-Handed
                </option>
                <option value="clock" data-i18n="events.clock">Clock</option>
                <option value="minx" data-i18n="events.minx">Megaminx</option>
                <option value="pyram" data-i18n="events.pyram">Pyraminx</option>
                <option value="skewb" data-i18n="events.skewb">Skewb</option>
                <option value="sq1" data-i18n="events.sq1">Square-1</option>
              </optgroup>
              <optgroup label="Non-WCA Puzzles">
                <option value="fto">Face-Turning Octahedron</option>
                <option value="master_tetraminx">Master Tetraminx</option>
                <option value="kilominx">Kilominx</option>
                <option value="redi_cube">Redi Cube</option>
                <option value="baby_fto">Baby FTO</option>
              </optgroup>
            </select>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" id="new-session-save" class="action-button">
            <i class="fas fa-check"></i>
            <span data-i18n="sessions.create">Create</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Edit Session Modal -->
    <div class="modal" id="edit-session-modal">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-title" data-i18n="sessions.editSessionTitle">
            Edit Session
          </div>
          <button
            type="button"
            class="modal-close"
            id="edit-session-close"
            title="Close"
          >
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="input-group">
            <label for="edit-session-name" data-i18n="sessions.sessionName"
              >Session Name:</label
            >
            <input
              type="text"
              id="edit-session-name"
              class="session-input"
              placeholder="My Session"
              data-i18n-placeholder="sessions.sessionNamePlaceholder"
            />
          </div>
          <div class="input-group">
            <label for="edit-session-puzzle" data-i18n="sessions.puzzleType"
              >Puzzle Type:</label
            >
            <select id="edit-session-puzzle" class="session-input">
              <optgroup label="WCA Puzzles">
                <option value="222" data-i18n="events.222">2x2x2</option>
                <option value="333" data-i18n="events.333">3x3x3</option>
                <option value="444" data-i18n="events.444">4x4x4</option>
                <option value="555" data-i18n="events.555">5x5x5</option>
                <option value="666" data-i18n="events.666">6x6x6</option>
                <option value="777" data-i18n="events.777">7x7x7</option>
                <option value="333oh" data-i18n="events.333oh">
                  3x3x3 One-Handed
                </option>
                <option value="clock" data-i18n="events.clock">Clock</option>
                <option value="minx" data-i18n="events.minx">Megaminx</option>
                <option value="pyram" data-i18n="events.pyram">Pyraminx</option>
                <option value="skewb" data-i18n="events.skewb">Skewb</option>
                <option value="sq1" data-i18n="events.sq1">Square-1</option>
              </optgroup>
              <optgroup label="Non-WCA Puzzles">
                <option value="fto">Face-Turning Octahedron</option>
                <option value="master_tetraminx">Master Tetraminx</option>
                <option value="kilominx">Kilominx</option>
                <option value="redi_cube">Redi Cube</option>
                <option value="baby_fto">Baby FTO</option>
              </optgroup>
            </select>
          </div>
          <input type="hidden" id="edit-session-id" />
        </div>
        <div class="modal-footer">
          <div class="modal-footer-buttons">
            <button type="button" id="edit-session-save" class="action-button">
              <i class="fas fa-check"></i>
              <span data-i18n="sessions.save">Save</span>
            </button>
            <button
              type="button"
              id="edit-session-delete"
              class="delete-button"
            >
              <i class="fas fa-trash-alt"></i>
              <span data-i18n="sessions.delete">Delete</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Statistics Details Panel -->
    <div class="stats-details-panel" id="stats-details-panel">
      <div class="stats-details-header">
        <div class="stats-details-title-container">
          <span
            class="stats-details-title-text"
            data-i18n="statsDetails.titleFor"
            >Statistics Details for</span
          >
          <div class="stats-event-selector">
            <button
              type="button"
              class="stats-event-selector-btn"
              id="stats-event-selector-btn"
              title="Select Event/Session"
            >
              <span class="cubing-icon event-333" id="stats-event-icon"></span>
              <span class="stats-event-text" id="stats-event-text">3x3x3</span>
            </button>
            <div class="stats-event-dropdown" id="stats-event-dropdown">
              <!-- Options will be populated dynamically -->
            </div>
          </div>
        </div>
        <button
          type="button"
          class="stats-details-close"
          id="stats-details-close"
          title="Close Statistics"
        >
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="stats-details-content">
        <!-- Single Event/Session View -->
        <div id="single-stats-view">
          <!-- Row 1: Overview and Averages -->
          <div class="stats-row">
            <!-- Overview Section -->
            <div class="stats-section">
              <h3 class="stats-section-title" data-i18n="statsDetails.overview">
                Overview
              </h3>
              <div class="stats-grid">
                <div class="stat-card">
                  <div class="stat-card-label" data-i18n="stats.solves">
                    Solves
                  </div>
                  <div class="stat-card-value" id="detail-solve-count">0</div>
                </div>
                <div class="stat-card">
                  <div class="stat-card-label" data-i18n="stats.best">Best</div>
                  <div class="stat-card-value" id="detail-best-time">-</div>
                </div>
                <div class="stat-card">
                  <div class="stat-card-label" data-i18n="stats.worst">
                    Worst
                  </div>
                  <div class="stat-card-value" id="detail-worst-time">-</div>
                </div>
                <div class="stat-card">
                  <div class="stat-card-label" data-i18n="stats.mean">Mean</div>
                  <div class="stat-card-value" id="detail-mean-time">-</div>
                </div>
                <div class="stat-card">
                  <div class="stat-card-label" data-i18n="stats.bestMo3">
                    Best mo3
                  </div>
                  <div class="stat-card-value" id="detail-best-mo3">-</div>
                </div>
              </div>
            </div>

            <!-- Averages Section -->
            <div class="stats-section">
              <h3 class="stats-section-title" data-i18n="statsDetails.averages">
                Averages
              </h3>
              <div class="stats-grid">
                <div class="stat-card">
                  <div class="stat-card-label" data-i18n="stats.avg5">ao5</div>
                  <div class="stat-card-value" id="detail-avg5">-</div>
                </div>
                <div class="stat-card">
                  <div class="stat-card-label" data-i18n="stats.avg12">
                    ao12
                  </div>
                  <div class="stat-card-value" id="detail-avg12">-</div>
                </div>
                <div class="stat-card">
                  <div class="stat-card-label" data-i18n="stats.avg100">
                    ao100
                  </div>
                  <div class="stat-card-value" id="detail-avg100">-</div>
                </div>
                <div class="stat-card">
                  <div class="stat-card-label" data-i18n="stats.avg1000">
                    ao1000
                  </div>
                  <div class="stat-card-value" id="detail-avg1000">-</div>
                </div>
                <div class="stat-card">
                  <div
                    class="stat-card-label"
                    data-i18n="statsDetails.standardDeviation"
                  >
                    Std Dev
                  </div>
                  <div class="stat-card-value" id="detail-std-dev">-</div>
                </div>
              </div>
            </div>
          </div>

          <!-- Row 2: Records -->
          <div class="stats-row">
            <div class="stats-section">
              <h3 class="stats-section-title" data-i18n="statsDetails.records">
                Records
              </h3>
              <div class="records-list">
                <div class="record-item">
                  <div class="record-label" data-i18n="statsDetails.bestSingle">
                    Best Single:
                  </div>
                  <div class="record-value" id="record-best-single">-</div>
                </div>
                <div class="record-item">
                  <div class="record-label" data-i18n="statsDetails.bestAo5">
                    Best ao5:
                  </div>
                  <div class="record-value" id="record-best-ao5">-</div>
                </div>
                <div class="record-item">
                  <div class="record-label" data-i18n="statsDetails.bestAo12">
                    Best ao12:
                  </div>
                  <div class="record-value" id="record-best-ao12">-</div>
                </div>
                <div class="record-item">
                  <div class="record-label" data-i18n="statsDetails.bestAo100">
                    Best ao100:
                  </div>
                  <div class="record-value" id="record-best-ao100">-</div>
                </div>
                <div class="record-item">
                  <div class="record-label" data-i18n="statsDetails.bestAo1000">
                    Best ao1000:
                  </div>
                  <div class="record-value" id="record-best-ao1000">-</div>
                </div>
              </div>
            </div>
          </div>

          <!-- Row 3: Charts -->
          <div class="stats-row">
            <!-- Time Distribution Chart -->
            <div class="stats-section">
              <h3
                class="stats-section-title"
                data-i18n="statsDetails.timeDistribution"
              >
                Time Distribution
              </h3>
              <div class="chart-container">
                <canvas
                  id="time-distribution-chart"
                  width="400"
                  height="200"
                ></canvas>
              </div>
            </div>

            <!-- Progress Chart -->
            <div class="stats-section">
              <h3
                class="stats-section-title"
                data-i18n="statsDetails.progressChart"
              >
                Progress Over Time
              </h3>
              <div class="chart-container">
                <canvas id="progress-chart" width="400" height="200"></canvas>
              </div>
            </div>
          </div>

          <!-- Row 4: Session Analysis and Predictions -->
          <div class="stats-row">
            <!-- Session Analysis -->
            <div class="stats-section">
              <h3
                class="stats-section-title"
                data-i18n="statsDetails.sessionAnalysis"
              >
                Session Analysis
              </h3>
              <div class="analysis-grid">
                <div class="analysis-item">
                  <div
                    class="analysis-label"
                    data-i18n="statsDetails.totalTime"
                  >
                    Total Time:
                  </div>
                  <div class="analysis-value" id="analysis-total-time">-</div>
                </div>
                <div class="analysis-item">
                  <div
                    class="analysis-label"
                    data-i18n="statsDetails.averageTime"
                  >
                    Average Time:
                  </div>
                  <div class="analysis-value" id="analysis-average-time">-</div>
                </div>
                <div class="analysis-item">
                  <div
                    class="analysis-label"
                    data-i18n="statsDetails.solvesPerHour"
                  >
                    Solves/Hour:
                  </div>
                  <div class="analysis-value" id="analysis-solves-per-hour">
                    -
                  </div>
                </div>
                <div class="analysis-item">
                  <div
                    class="analysis-label"
                    data-i18n="statsDetails.consistency"
                  >
                    Consistency:
                  </div>
                  <div class="analysis-value" id="analysis-consistency">-</div>
                </div>
              </div>
            </div>

            <!-- Future Expectations -->
            <div class="stats-section">
              <h3
                class="stats-section-title"
                data-i18n="statsDetails.predictions"
              >
                Predictions
              </h3>
              <div class="predictions-grid">
                <div class="prediction-item">
                  <div
                    class="prediction-label"
                    data-i18n="statsDetails.nextAo5"
                  >
                    Next ao5 Target:
                  </div>
                  <div class="prediction-value" id="prediction-ao5">-</div>
                </div>
                <div class="prediction-item">
                  <div
                    class="prediction-label"
                    data-i18n="statsDetails.nextAo12"
                  >
                    Next ao12 Target:
                  </div>
                  <div class="prediction-value" id="prediction-ao12">-</div>
                </div>
                <div class="prediction-item">
                  <div
                    class="prediction-label"
                    data-i18n="statsDetails.improvementRate"
                  >
                    Improvement Rate:
                  </div>
                  <div class="prediction-value" id="prediction-improvement">
                    -
                  </div>
                </div>
                <div class="prediction-item">
                  <div
                    class="prediction-label"
                    data-i18n="statsDetails.targetTime"
                  >
                    Target Time:
                  </div>
                  <div class="prediction-value" id="prediction-target">-</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- All Events Overview -->
        <div id="all-events-view" style="display: none">
          <div class="all-events-list" id="all-events-list">
            <!-- Events will be populated here -->
          </div>
        </div>

        <!-- Row 5: Import/Export and Session Management -->
        <div class="stats-row">
          <div class="stats-import-export-section">
            <div class="stats-section-title">Import/Export</div>
            <div class="import-export-buttons">
              <button
                type="button"
                class="import-export-btn"
                id="import-times-btn"
              >
                <i class="fas fa-file-import"></i>
                <span data-i18n="statsDetails.importTimes">Import Times</span>
              </button>
              <button
                type="button"
                class="import-export-btn"
                id="export-json-btn"
              >
                <i class="fas fa-file-export"></i>
                <span data-i18n="statsDetails.exportJSON">Export JSON</span>
              </button>
              <button
                type="button"
                class="import-export-btn"
                id="export-csv-btn"
              >
                <i class="fas fa-file-csv"></i>
                <span data-i18n="statsDetails.exportCSV">Export CSV</span>
              </button>
              <button
                type="button"
                class="import-export-btn"
                id="stats-edit-session-btn"
                style="display: none"
              >
                <i class="fas fa-edit"></i>
                <span data-i18n="statsDetails.editSession">Edit Session</span>
              </button>
              <button
                type="button"
                class="import-export-btn"
                id="stats-empty-session-btn"
                style="display: none"
              >
                <i class="fas fa-trash"></i>
                <span data-i18n="statsDetails.emptySession">Empty Session</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- csTimer Import Modal -->
    <div class="modal" id="cstimer-import-modal">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-title">Import csTimer Data</div>
          <button
            type="button"
            class="modal-close"
            id="cstimer-import-close"
            title="Close"
          >
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="import-info">
            <p>
              Found <span id="cstimer-times-count">0</span> times in csTimer
              format.
            </p>
            <p>Choose where to import these times:</p>
          </div>
          <div class="input-group">
            <label for="cstimer-event-select">Event/Puzzle Type:</label>
            <select id="cstimer-event-select" class="session-input">
              <optgroup label="WCA Puzzles">
                <option value="222">2x2x2</option>
                <option value="333" selected>3x3x3</option>
                <option value="444">4x4x4</option>
                <option value="555">5x5x5</option>
                <option value="666">6x6x6</option>
                <option value="777">7x7x7</option>
                <option value="333oh">3x3x3 One-Handed</option>
                <option value="333bf">3x3x3 Blindfolded</option>
                <option value="333fm">3x3x3 Fewest Moves</option>
                <option value="clock">Clock</option>
                <option value="minx">Megaminx</option>
                <option value="pyram">Pyraminx</option>
                <option value="skewb">Skewb</option>
                <option value="sq1">Square-1</option>
                <option value="444bf">4x4x4 Blindfolded</option>
                <option value="555bf">5x5x5 Blindfolded</option>
                <option value="333mbf">3x3x3 Multi-Blind</option>
              </optgroup>
              <optgroup label="Non-WCA Puzzles">
                <option value="fto">FTO</option>
                <option value="master_tetraminx">Master Tetraminx</option>
                <option value="kilominx">Kilominx</option>
                <option value="redi_cube">Redi Cube</option>
                <option value="baby_fto">Baby FTO</option>
              </optgroup>
            </select>
          </div>
          <div class="input-group">
            <label>Import Destination:</label>
            <div class="radio-group">
              <label class="radio-option">
                <input
                  type="radio"
                  name="import-destination"
                  value="existing-event"
                  checked
                />
                <span>Add to existing event times</span>
              </label>
              <label class="radio-option">
                <input
                  type="radio"
                  name="import-destination"
                  value="new-session"
                />
                <span>Create new session</span>
              </label>
              <label
                class="radio-option"
                id="existing-session-option"
                style="display: none"
              >
                <input
                  type="radio"
                  name="import-destination"
                  value="existing-session"
                />
                <span>Add to existing session</span>
              </label>
            </div>
          </div>
          <div class="input-group" id="new-session-group" style="display: none">
            <label for="cstimer-session-name">Session Name:</label>
            <input
              type="text"
              id="cstimer-session-name"
              class="session-input"
              placeholder="csTimer Import"
            />
          </div>
          <div
            class="input-group"
            id="existing-session-group"
            style="display: none"
          >
            <label for="cstimer-session-select">Select Session:</label>
            <select id="cstimer-session-select" class="session-input">
              <!-- Sessions will be populated dynamically -->
            </select>
          </div>
        </div>
        <div class="modal-footer">
          <button
            type="button"
            id="cstimer-import-confirm"
            class="action-button"
          >
            <i class="fas fa-file-import"></i>
            <span>Import Times</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Stat Detail Modal -->
    <div class="modal" id="stat-detail-modal">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-title" id="stat-detail-title">
            Statistic Details
          </div>
          <button
            type="button"
            class="modal-close"
            id="stat-detail-close"
            title="Close"
          >
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="stat-detail-info">
            <div class="stat-detail-item">
              <span class="stat-detail-label">Time:</span>
              <span class="stat-detail-value" id="stat-detail-value">-</span>
              <button
                type="button"
                class="stat-share-btn"
                id="stat-detail-share"
                title="Share this statistic"
                style="display: none"
              >
                <i class="fas fa-share-alt"></i>
                <span>Share</span>
              </button>
            </div>
            <div class="stat-detail-item" id="stat-detail-datetime-item">
              <span class="stat-detail-label" id="stat-detail-datetime-label"
                >Date & Time:</span
              >
              <span class="stat-detail-value" id="stat-detail-datetime">-</span>
            </div>
          </div>
          <div
            class="stat-detail-scramble-section"
            id="stat-detail-single-scramble-section"
          >
            <div class="stat-detail-scramble-header">
              <span class="stat-detail-label">Scramble:</span>
              <button
                type="button"
                class="copy-scramble-btn"
                id="stat-detail-copy-scramble"
                title="Copy scramble"
              >
                <i class="fas fa-copy"></i>
              </button>
            </div>
            <div class="stat-detail-scramble" id="stat-detail-scramble">-</div>
          </div>
          <div
            class="stat-detail-scramble-section"
            id="stat-detail-single-solution-section"
            style="display: none"
          >
            <div class="stat-detail-scramble-header">
              <span class="stat-detail-label">Solution:</span>
              <button
                type="button"
                class="copy-scramble-btn"
                id="stat-detail-copy-solution"
                title="Copy solution"
              >
                <i class="fas fa-copy"></i>
              </button>
            </div>
            <div class="stat-detail-scramble" id="stat-detail-solution">-</div>
          </div>
          <div
            class="stat-detail-average-info"
            id="stat-detail-average-info"
            style="display: none"
          >
            <div class="stat-detail-duration-info">
              <div class="stat-detail-item stat-detail-datetime-range">
                <span class="stat-detail-label">Date & Time:</span>
                <span class="stat-detail-value" id="stat-detail-datetime-range"
                  >-</span
                >
              </div>
            </div>
            <div class="stat-detail-scrambles-section">
              <div class="stat-detail-label">Scrambles & Times:</div>
              <div
                class="stat-detail-scrambles-list"
                id="stat-detail-scrambles-list"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Times toggle button (outside all containers) -->
    <button
      type="button"
      class="times-toggle"
      id="times-toggle"
      title="View Times"
    >
      <i class="fas fa-history"></i>
    </button>

    <!-- Audio elements for inspection sounds -->
    <audio id="beep-sound" preload="auto">
      <source src="assets/sounds/8sec.wav" type="audio/wav" />
    </audio>
    <audio id="double-beep-sound" preload="auto">
      <source src="assets/sounds/12sec.wav" type="audio/wav" />
    </audio>

    <!-- Audio elements are used by the main code -->

    <!-- Audio elements are kept for fallback compatibility -->

    <!-- Stackmat Timer Library -->
    <script src="assets/js/stackmat.min.js"></script>
    <script src="assets/js/stackmat-manager.js"></script>

    <!-- Tutorial System -->
    <script src="assets/js/tutorial-manager.js"></script>

    <!-- Custom JavaScript -->
    <script type="module">
      // Import the language manager
      import { initLanguageManager } from "./assets/js/language-manager.js";

      // Initialize the language manager
      document.addEventListener("DOMContentLoaded", async () => {
        await initLanguageManager();
      });
    </script>
    <!-- Google Drive API -->
    <script src="https://apis.google.com/js/api.js"></script>
    <script src="https://accounts.google.com/gsi/client" async defer></script>

    <!-- AnimCube3.js for 3D cube visualization -->
    <script src="assets/animCubeJS/AnimCube3.js"></script>

    <script type="module" src="assets/js/sync-manager.js"></script>
    <script type="module" src="assets/js/cubing-timer.js"></script>
    <!-- MBLD Manager is imported by cubing-timer.js -->

    <!-- Service Worker Registration -->
    <script>
      if ("serviceWorker" in navigator) {
        window.addEventListener("load", () => {
          navigator.serviceWorker
            .register("service-worker.js?v=" + new Date().getTime())
            .then(() => {
              // Registration successful
            })
            .catch(() => {
              // Registration failed
            });
        });
      }
    </script>
  </body>
</html>
