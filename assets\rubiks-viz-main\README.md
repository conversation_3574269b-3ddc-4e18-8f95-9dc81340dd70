# rubiks-viz

Try the demo: https://spencerchubb.github.io/rubiks-viz/

Features and fun facts
- rubiks viz is the library that powers cubingapp.com
- Can be used in any Javascript framework (React, Angular, Vue, Svelte, etc). You just need a reference to a div
- Uses WebGL for rendering</li>
- Any size nxn and pyraminx
- Can render multiple cubes in a page
- Turn by clicking and dragging
- Turn with keyboard

## TODO
- publish to npm
- open to other suggestions if you want to use the library and have ideas
