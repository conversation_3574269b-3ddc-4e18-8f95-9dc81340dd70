(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const n of document.querySelectorAll('link[rel="modulepreload"]'))s(n);new MutationObserver(n=>{for(const h of n)if(h.type==="childList")for(const c of h.addedNodes)c.tagName==="LINK"&&c.rel==="modulepreload"&&s(c)}).observe(document,{childList:!0,subtree:!0});function e(n){const h={};return n.integrity&&(h.integrity=n.integrity),n.referrerPolicy&&(h.referrerPolicy=n.referrerPolicy),n.crossOrigin==="use-credentials"?h.credentials="include":n.crossOrigin==="anonymous"?h.credentials="omit":h.credentials="same-origin",h}function s(n){if(n.ep)return;n.ep=!0;const h=e(n);fetch(n.href,h)}})();function Re(i){return i.split(" ").map(t=>{if(t.endsWith("2")){let e=t.slice(0,-1);return e+" "+e}else if(t.endsWith("2'")){let e=t.slice(0,-2)+"'";return e+" "+e}else if(t.endsWith("3")){let e=t.slice(0,-1);return e+" "+e+" "+e}else if(t.endsWith("3'")){let e=t.slice(0,-2)+"'";return e+" "+e+" "+e}return t}).join(" ")}const Se=[1,1,1],ne=[1,1,0],he=[0,1,0],ce=[0,0,1],ge=[1,.5,0],oe=[1,0,0],ke=[0,0,0];function H(i,t){const e=i.createBuffer();return i.bindBuffer(i.ARRAY_BUFFER,e),i.bufferData(i.ARRAY_BUFFER,new Float32Array(t),i.STATIC_DRAW),e}class re{constructor(t,e){this.base=H(t,e.baseVertices),this.sticker=H(t,e.stickerVertices),this.hint=H(t,e.hintVertices),this.color=this.getColorBuffer(t,e.color),this.black=this.getColorBuffer(t,ke);const s=[0,1,2,0,2,3];this.indexBuffer=t.createBuffer(),t.bindBuffer(t.ELEMENT_ARRAY_BUFFER,this.indexBuffer),t.bufferData(t.ELEMENT_ARRAY_BUFFER,new Uint16Array(s),t.STATIC_DRAW),this.cart2d=Fe(e.baseVertices,e.perspective)}}class Le extends re{drawElement(t){t.drawElements(t.TRIANGLES,3,t.UNSIGNED_SHORT,0)}getColorBuffer(t,e){return H(t,[e[0],e[1],e[2],1,e[0],e[1],e[2],1,e[0],e[1],e[2],1])}}class Ae extends re{drawElement(t){t.drawElements(t.TRIANGLES,6,t.UNSIGNED_SHORT,0)}getColorBuffer(t,e){return H(t,[e[0],e[1],e[2],1,e[0],e[1],e[2],1,e[0],e[1],e[2],1,e[0],e[1],e[2],1])}}function Fe(i,t){const e=i,s=Array(16);return Et(s,0,t,[e[0],e[1],e[2],1]),Et(s,4,t,[e[3],e[4],e[5],1]),Et(s,8,t,[e[6],e[7],e[8],1]),Et(s,12,t,[e[9],e[10],e[11],1]),[s[0]/s[3],s[1]/s[3],s[4]/s[7],s[5]/s[7],s[8]/s[11],s[9]/s[11],s[12]/s[15],s[13]/s[15]]}function Et(i,t,e,s){let n=s[0],h=s[1],c=s[2],a=s[3];i[t+0]=n*e[0]+h*e[4]+c*e[8]+a*e[12],i[t+1]=n*e[1]+h*e[5]+c*e[9]+a*e[13],i[t+2]=n*e[2]+h*e[6]+c*e[10]+a*e[14],i[t+3]=n*e[3]+h*e[7]+c*e[11]+a*e[15]}const Ct={};function x(i){return Math.floor(i/2)}function ft(i){return i%2==0}function Te(i,t){const e=t.layers,s=t.perspective,n=`${e}-${s}`;if(Ct[n])return Ct[n];let h=Kt(e,1,0),c=Kt(e,1.01,.02),a=Kt(e,1.5,.02);const r=Array(t.numStickers()),o=t.layers*t.layers;for(let l=0;l<t.numStickers();l++){let u={perspective:s,color:l<o?Se:l<o*2?he:l<o*3?ne:l<o*4?ce:l<o*5?ge:oe,baseVertices:[],stickerVertices:[],hintVertices:[]};for(let y=0;y<12;y++){let m=l*12+y;u.baseVertices[y]=h[m],u.stickerVertices[y]=c[m],u.hintVertices[y]=a[m]}r[l]=new Ae(i,u)}return Ct[n]=r,r}const le=12,Oe=4,j=3;function Kt(i,t,e){const s=p(i)*le,n=Array(6*s);return ve(n,0*s,i,1,t,e),Ee(n,1*s,i,0,t,e),Pe(n,2*s,i,1,-t,e),Me(n,3*s,i,0,-t,e),Be(n,4*s,i,2,-t,e),Ue(n,5*s,i,2,t,e),n}function ve(i,t,e,s,n,h){if(ft(e)){let r=Array(p(e)),o=0;for(let l=0;l<e;l++)for(let u=0;u<e;u++){const y=-1+1/e+u*2/e,m=-1+1/e+l*2/e;r[o]=[y,m,n],o++}A(i,t,e,r,s,h);return}let c=Array(p(e)),a=0;for(let r=-x(e);r<=x(e);r++)for(let o=-x(e);o<=x(e);o++)c[a]=[2*o/e,2*r/e,n],a++;A(i,t,e,c,s,h)}function Ee(i,t,e,s,n,h){if(ft(e)){let r=Array(p(e)),o=0;for(let l=0;l<e;l++)for(let u=e-1;u>=0;u--){const y=-1+1/e+l*2/e,m=-1+1/e+u*2/e;r[o]=[y,m,n],o++}A(i,t,e,r,s,h);return}let c=Array(p(e)),a=0;for(let r=-x(e);r<=x(e);r++)for(let o=x(e);o>=-x(e);o--)c[a]=[2*r/e,2*o/e,n],a++;A(i,t,e,c,s,h)}function Pe(i,t,e,s,n,h){if(ft(e)){let r=Array(p(e)),o=0;for(let l=0;l<e;l++)for(let u=e-1;u>=0;u--){const y=-1+1/e+u*2/e,m=-1+1/e+l*2/e;r[o]=[y,m,n],o++}A(i,t,e,r,s,h);return}let c=Array(p(e)),a=0;for(let r=-x(e);r<=x(e);r++)for(let o=x(e);o>=-x(e);o--)c[a]=[2*o/e,2*r/e,n],a++;A(i,t,e,c,s,h)}function Me(i,t,e,s,n,h){if(ft(e)){let r=Array(p(e)),o=0;for(let l=0;l<e;l++)for(let u=0;u<e;u++){const y=-1+1/e+l*2/e,m=-1+1/e+u*2/e;r[o]=[y,m,n],o++}A(i,t,e,r,s,h);return}let c=Array(p(e)),a=0;for(let r=-x(e);r<=x(e);r++)for(let o=-x(e);o<=x(e);o++)c[a]=[2*r/e,2*o/e,n],a++;A(i,t,e,c,s,h)}function Be(i,t,e,s,n,h){if(ft(e)){let r=Array(p(e)),o=0;for(let l=0;l<e;l++)for(let u=e-1;u>=0;u--){const y=-1+1/e+u*2/e,m=-1+1/e+l*2/e;r[o]=[y,m,n],o++}A(i,t,e,r,s,h);return}let c=Array(p(e)),a=0;for(let r=-x(e);r<=x(e);r++)for(let o=x(e);o>=-x(e);o--)c[a]=[2*o/e,2*r/e,n],a++;A(i,t,e,c,s,h)}function Ue(i,t,e,s,n,h){if(ft(e)){let r=Array(p(e)),o=0;for(let l=e-1;l>=0;l--)for(let u=e-1;u>=0;u--){const y=-1+1/e+u*2/e,m=-1+1/e+l*2/e;r[o]=[y,m,n],o++}A(i,t,e,r,s,h);return}let c=Array(p(e)),a=0;for(let r=x(e);r>=-x(e);r--)for(let o=x(e);o>=-x(e);o--)c[a]=[2*o/e,2*r/e,n],a++;A(i,t,e,c,s,h)}function A(i,t,e,s,n,h){for(let c=0;c<p(e);c++){const a=s[c];_e(i,t+c*le,e,a[0],a[1],a[2],n,h)}}function _e(i,t,e,s,n,h,c,a){const r=1/e-a,o=[[s-r,n-r,h],[s+r,n-r,h],[s+r,n+r,h],[s-r,n+r,h]];for(let l=0;l<Oe;l++){const u=o[l];i[t+l*j]=u[(c+0)%j],i[t+l*j+1]=u[(c+1)%j],i[t+l*j+2]=u[(c+2)%j]}}function Ce(){const i=localStorage.getItem("keyBindings");return i?JSON.parse(i):{...Ke}}const Ke={KeyN:"x",KeyB:"x'",Semicolon:"y",KeyA:"y'",KeyP:"z",KeyQ:"z'",KeyJ:"U",KeyF:"U'",KeyS:"D",KeyL:"D'",KeyH:"F",KeyG:"F'",KeyW:"B",KeyO:"B'",KeyD:"L",KeyE:"L'",KeyI:"R",KeyK:"R'",BracketLeft:"M",Quote:"M'",KeyC:"E",Comma:"E'",KeyY:"S",KeyT:"S'",KeyU:"r",KeyM:"r'",KeyV:"l",KeyR:"l'"};class ue{constructor(t){this.animationQueue=[],this.perspective=t,this.keyBindings=Ce()}resetAffectedStickers(){this.affectedStickers=Array(this.numStickers()).fill(!1)}naiveScramble(){let t=this.numStickers()*5,e=this.getMoveMap(!0);for(let s=0;s<t;s++){let n=Math.floor(Math.random()*Object.keys(e).length),h=Object.values(e)[n];h()}}getStickers(){return this.animationQueue.length>0?this.animationQueue[0].stickers:this.stickers}matchKeyToTurn(t){if(t.ctrlKey)return;const e=this.keyBindings[t.code];this.performMove(e,!0)}getMoveMap(t){return{x:()=>this.x(t),"x'":()=>this.x(!t),x2:()=>{this.x(t),this.x(t)},"x2'":()=>{this.x(!t),this.x(!t)},y:()=>this.y(t),"y'":()=>this.y(!t),y2:()=>{this.y(t),this.y(t)},"y2'":()=>{this.y(!t),this.y(!t)},z:()=>this.z(t),"z'":()=>this.z(!t),z2:()=>{this.z(t),this.z(t)},"z2'":()=>{this.z(!t),this.z(!t)},U:()=>this.U(t),"U'":()=>this.U(!t),U2:()=>{this.U(t),this.U(t)},"U2'":()=>{this.U(!t),this.U(!t)},Uw:()=>this.Uw(t),"Uw'":()=>this.Uw(!t),Uw2:()=>{this.Uw(t),this.Uw(t)},"Uw2'":()=>{this.Uw(!t),this.Uw(!t)},u:()=>this.Uw(t),"u'":()=>this.Uw(!t),u2:()=>{this.Uw(t),this.Uw(t)},"u2'":()=>{this.Uw(!t),this.Uw(!t)},D:()=>this.D(t),"D'":()=>this.D(!t),D2:()=>{this.D(t),this.D(t)},"D2'":()=>{this.D(!t),this.D(!t)},Dw:()=>this.Dw(t),"Dw'":()=>this.Dw(!t),Dw2:()=>{this.Dw(t),this.Dw(t)},"Dw2'":()=>{this.Dw(!t),this.Dw(!t)},d:()=>this.Dw(t),"d'":()=>this.Dw(!t),d2:()=>{this.Dw(t),this.Dw(t)},"d2'":()=>{this.Dw(!t),this.Dw(!t)},F:()=>this.F(t),"F'":()=>this.F(!t),F2:()=>{this.F(t),this.F(t)},"F2'":()=>{this.F(!t),this.F(!t)},Fw:()=>this.Fw(t),"Fw'":()=>this.Fw(!t),Fw2:()=>{this.Fw(t),this.Fw(t)},"Fw2'":()=>{this.Fw(!t),this.Fw(!t)},f:()=>this.Fw(t),"f'":()=>this.Fw(!t),f2:()=>{this.Fw(t),this.Fw(t)},"f2'":()=>{this.Fw(!t),this.Fw(!t)},B:()=>this.B(t),"B'":()=>this.B(!t),B2:()=>{this.B(t),this.B(t)},"B2'":()=>{this.B(!t),this.B(!t)},Bw:()=>this.Bw(t),"Bw'":()=>this.Bw(!t),Bw2:()=>{this.Bw(t),this.Bw(t)},"Bw2'":()=>{this.Bw(!t),this.Bw(!t)},b:()=>this.Bw(t),"b'":()=>this.Bw(!t),b2:()=>{this.Bw(t),this.Bw(t)},"b2'":()=>{this.Bw(!t),this.Bw(!t)},L:()=>this.L(t),"L'":()=>this.L(!t),L2:()=>{this.L(t),this.L(t)},"L2'":()=>{this.L(!t),this.L(!t)},L3:()=>this.L(!t),"L3'":()=>this.L(t),Lw:()=>this.Lw(t),"Lw'":()=>this.Lw(!t),Lw2:()=>{this.Lw(t),this.Lw(t)},"Lw2'":()=>{this.Lw(!t),this.Lw(!t)},l:()=>this.Lw(t),"l'":()=>this.Lw(!t),l2:()=>{this.Lw(t),this.Lw(t)},"l2'":()=>{this.Lw(!t),this.Lw(!t)},R:()=>this.R(t),"R'":()=>this.R(!t),R2:()=>{this.R(t),this.R(t)},"R2'":()=>{this.R(!t),this.R(!t)},R3:()=>this.R(!t),"R3'":()=>this.R(t),Rw:()=>this.Rw(t),"Rw'":()=>this.Rw(!t),Rw2:()=>{this.Rw(t),this.Rw(t)},"Rw2'":()=>{this.Rw(!t),this.Rw(!t)},r:()=>this.Rw(t),"r'":()=>this.Rw(!t),r2:()=>{this.Rw(t),this.Rw(t)},"r2'":()=>{this.Rw(!t),this.Rw(!t)},M:()=>this.M(t),"M'":()=>this.M(!t),M2:()=>{this.M(t),this.M(t)},"M2'":()=>{this.M(!t),this.M(!t)},E:()=>this.E(t),"E'":()=>this.E(!t),E2:()=>{this.E(t),this.E(t)},"E2'":()=>{this.E(!t),this.E(!t)},S:()=>this.S(t),"S'":()=>this.S(!t),S2:()=>{this.S(t),this.S(t)},"S2'":()=>{this.S(!t),this.S(!t)}}}performMove(t,e){const s=this.getMoveMap(e)[t];s?s():console.log("Invalid move: "+t)}performAlg(t){if(!t)return console.log("Empty alg. Skipping."),0;let e=t.split(" ");for(let s=0;s<e.length;s++)this.performMove(e[s],!0),this.animationQueue=[];return e.length}performAlgWithAnimation(t,e){t=Re(t);const s=800;let n=t.split(" "),h=0;return setInterval(()=>{if(h>=n.length){e();return}this.performMove(n[h],!0),h++},s)}}function p(i){return i*i}class Ie extends ue{constructor(t,e,s){super(e),this.layers=s;const n=this.numStickers();this.stickers=Array(n);for(let h=0;h<n;h++)this.stickers[h]=h;this.resetAffectedStickers(),this.shapes=Te(t,this)}getHintType(t){return this.hintType||(this.hintType=H(t,[1,1,1,1])),this.hintType}getShapes(){return this.shapes}numStickers(){return this.layers*this.layers*6}startDraggable(){return 0}endDraggable(){return p(this.layers)*2}x(t){this.cubeRotate(0,t)}y(t){this.cubeRotate(1,t)}z(t){this.cubeRotate(2,t)}U(t){this.turn(1,0,t)}Uw(t){this.wideTurn(1,0,1,t)}D(t){this.turn(1,this.layers-1,!t)}Dw(t){this.wideTurn(1,this.layers-1,this.layers-2,!t)}F(t){this.turn(2,0,t)}Fw(t){this.wideTurn(2,0,1,t)}B(t){this.turn(2,this.layers-1,!t)}Bw(t){this.wideTurn(2,this.layers-1,this.layers-2,!t)}R(t){this.turn(0,0,t)}Rw(t){this.wideTurn(0,0,1,t)}L(t){this.turn(0,this.layers-1,!t)}Lw(t){this.wideTurn(0,this.layers-1,this.layers-2,!t)}M(t){this.sliceTurn(0,!t)}E(t){this.sliceTurn(1,!t)}S(t){this.sliceTurn(2,t)}pushAnimation(t,e,s){let n=e?-1:1,h=[0,0,0];h[t]=n,this.animationQueue.push({axis:h,degrees:90,stickers:s,affectedStickers:this.affectedStickers})}turn(t,e,s){this.resetAffectedStickers(),this.pushAnimation(t,s,[...this.stickers]),this.matchTurn(t,e,s)}sliceTurn(t,e){this.resetAffectedStickers(),this.pushAnimation(t,e,[...this.stickers]);for(let s=1;s<this.layers-1;s++)this.matchTurn(t,s,e)}wideTurn(t,e,s,n){this.resetAffectedStickers(),this.pushAnimation(t,n,[...this.stickers]),this.matchTurn(t,e,n),this.matchTurn(t,s,n)}cubeRotate(t,e){this.resetAffectedStickers(),this.pushAnimation(t,e,[...this.stickers]);for(let s=0;s<this.layers;s++)this.matchTurn(t,s,e)}matchTurn(t,e,s){t==0?(this.turnXAxis(e,s),e==0?this.turnOuter(5,s):e==this.layers-1&&this.turnOuter(4,!s)):t==1?(this.turnYAxis(e,s),e==0?this.turnOuter(0,s):e==this.layers-1&&this.turnOuter(2,!s)):t==2?(this.turnZAxis(e,s),e==0?this.turnOuter(1,s):e==this.layers-1&&this.turnOuter(3,!s)):console.error(`Axis ${t} not recognized`)}turnXAxis(t,e){for(let s=1;s<=this.layers;s++)this.cycle(e,0*p(this.layers)+p(this.layers)-s-t*this.layers,3*p(this.layers)+p(this.layers)-s-t*this.layers,2*p(this.layers)+p(this.layers)-s-t*this.layers,1*p(this.layers)+p(this.layers)-s-t*this.layers)}turnYAxis(t,e){for(let s=0;s<this.layers;s++)this.cycle(e,1*p(this.layers)+s*this.layers+t,4*p(this.layers)+s*this.layers+t,3*p(this.layers)+(this.layers-s-1)*this.layers+(this.layers-1)-t,5*p(this.layers)+s*this.layers+t)}turnZAxis(t,e){for(let s=0;s<this.layers;s++)this.cycle(e,0*p(this.layers)+(s+1)*this.layers-1-t,5*p(this.layers)+s+this.layers*t,2*p(this.layers)+(this.layers-s-1)*this.layers+t,4*p(this.layers)+p(this.layers)-(s+1)-t*this.layers)}turnOuter(t,e){if(this.layers%2!=0){let s=this.center(t);this.affectedStickers[s]=!0}for(let s=0;s<Math.floor(this.layers/2);s++){const{topLeft:n,topRight:h,bottomLeft:c,bottomRight:a}=this.corners(t,s);this.cycle(e,n,h,a,c);let r=this.layers-2*(s+1);for(let o=0;o<r;o++){const{top:l,left:u,bottom:y,right:m}=this.edges(t,s,o);this.cycle(e,l,m,y,u)}}}cycle(t,...e){if(e.forEach(s=>this.affectedStickers[s]=!0),t){let s=this.stickers[e[e.length-1]];for(let n=e.length-1;n>0;n--)this.stickers[e[n]]=this.stickers[e[n-1]];this.stickers[e[0]]=s}else{let s=this.stickers[e[0]];for(let n=0;n<e.length-1;n++)this.stickers[e[n]]=this.stickers[e[n+1]];this.stickers[e[e.length-1]]=s}}stickerIsOnFace(t,e){return e*p(this.layers)<=t&&t<(e+1)*p(this.layers)}center(t){return t*p(this.layers)+Math.floor(p(this.layers)/2)}corners(t,e){const s=t*p(this.layers);return{topLeft:s+(this.layers+1)*e,topRight:s+(this.layers-1)*(this.layers-e),bottomRight:s+(this.layers+1)*(this.layers-e-1),bottomLeft:s+(this.layers-1)*(e+1)}}edges(t,e,s){const n=this.corners(t,e);let h=this.layers-2*(e+1);return{top:n.topLeft+this.layers*(s+1),left:n.topLeft+(h-s),right:n.topRight+s+1,bottom:n.bottomLeft+this.layers*(h-s)}}}function Xt(i,t){return i/t*2-1}function Jt(i,t){return 1-i/t*2}function P(i,t,e,s,n,h){return Math.abs(.5*(i*(s-h)+e*(h-t)+n*(t-s)))}function Zt(i,t){return Math.abs(i-t)<1e-4}class ae{onPointerDown(t,e,s,n){this.numOfPointerMoves=0;const h=Xt(t,s.clientWidth),c=Jt(e,s.clientHeight);this.xOnDown=h,this.yOnDown=c,[this.stickerOnDown,this.cart2dOnDown]=this.coordsToSticker(h,c,n),this.stickerOnDown===-1&&this._onPointerDown(h,c,n)}onPointerMove(t,e){this.numOfPointerMoves++,this.xOnMove=t,this.yOnMove=e}onPointerUp(t,e){if(this.numOfPointerMoves<2)return;const s=Xt(this.xOnMove,t.clientWidth),n=Jt(this.yOnMove,t.clientHeight);this._onPointerUp(s,n,e)}coordsToSticker(t,e,s){const n=s.getShapes(),h=r=>{const o=n[r].cart2d,[l,u,y,m,b,D,R,g]=o,k=P(l,u,y,m,b,D)+P(l,u,b,D,R,g),S=P(t,e,l,u,y,m),F=P(t,e,y,m,b,D),T=P(t,e,b,D,R,g),L=P(t,e,R,g,l,u);return Zt(k,S+F+T+L)?[r,o]:void 0},c=r=>{const o=n[r].cart2d,[l,u,y,m,b,D]=o,R=P(l,u,y,m,b,D),g=P(t,e,l,u,y,m),k=P(t,e,y,m,b,D),S=P(t,e,b,D,l,u);return Zt(R,g+k+S)?[r,o]:void 0},a=r=>{const o=n[r].cart2d;return Number.isNaN(o[6])||Number.isNaN(o[7])?c(r):h(r)};for(let r=s.startDraggable();r<s.endDraggable();r++){const o=a(r);if(o)return o}return[-1,void 0]}slope(t,e,s,n){return t<s?(n-e)/(s-t):(e-n)/(t-s)}}function Ve(i,t){return i.layers-1-t%i.layers}function It(i,t){return i.layers-1-Math.floor(t/i.layers)}function de(i,t){return t%i.layers}function Vt(i,t){return i.layers-1-Math.floor((t-p(i.layers))/i.layers)}class Ne extends ae{_onPointerDown(t,e,s){const n=s,h=s.getShapes();function c(m,b,D){return{x:h[m].cart2d[b],y:h[m].cart2d[D]}}const a=c(0,0,1),r=c(n.layers*(n.layers-1),6,7),o=c(n.layers-1,2,3),l=c(p(n.layers)-1,4,5),u=c(n.layers*(n.layers+1)-1,0,1),y=c(p(n.layers)*2-1,2,3);e>a.y&&t>a.x&&t<r.x?n.cubeRotate(0,!0):t<a.x&&e>o.y&&e<a.y?n.cubeRotate(2,!1):t>r.x&&e>l.y&&e<r.y?n.cubeRotate(2,!0):t<u.x&&e>u.y&&e<o.y?n.cubeRotate(1,!0):t>y.x&&e>y.y&&e<l.y?n.cubeRotate(1,!1):e<u.y&&t>u.x&&t<y.x&&n.cubeRotate(0,!1)}_onPointerUp(t,e,s){const n=s;let h,c;if(this.stickerOnDown!==-1&&(h=this.slope(this.cart2dOnDown[0],this.cart2dOnDown[1],this.cart2dOnDown[4],this.cart2dOnDown[5]),c=this.slope(this.cart2dOnDown[2],this.cart2dOnDown[3],this.cart2dOnDown[6],this.cart2dOnDown[7]),h<0)){const r=h;h=c,c=r}const a=this.slope(t,e,this.xOnDown,this.yOnDown);n.stickerIsOnFace(this.stickerOnDown,0)?t===this.xOnDown?n.turn(0,It(n,this.stickerOnDown),e>this.yOnDown):a>h?n.turn(0,It(n,this.stickerOnDown),t>this.xOnDown):a<c?n.turn(0,It(n,this.stickerOnDown),t<this.xOnDown):n.turn(2,Ve(n,this.stickerOnDown),t>this.xOnDown):n.stickerIsOnFace(this.stickerOnDown,1)&&(t===this.xOnDown?n.turn(0,Vt(n,this.stickerOnDown),e>this.yOnDown):a>h?n.turn(0,Vt(n,this.stickerOnDown),t>this.xOnDown):a<c?n.turn(0,Vt(n,this.stickerOnDown),t<this.xOnDown):n.turn(1,de(n,this.stickerOnDown),t<this.xOnDown))}}function Mt(){return[1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1]}function He(i,t,e,s,n){const h=1/Math.tan(t/2);if(i[0]=h/e,i[1]=0,i[2]=0,i[3]=0,i[4]=0,i[5]=h,i[6]=0,i[7]=0,i[8]=0,i[9]=0,i[11]=-1,i[12]=0,i[13]=0,i[15]=0,n!=null&&n!==1/0){const c=1/(s-n);i[10]=(n+s)*c,i[14]=2*n*s*c}else i[10]=-1,i[14]=-2*s;return i}function Bt(i,t,e,s){let n=s[0],h=s[1],c=s[2],a=1/Math.hypot(n,h,c);n*=a,h*=a,c*=a;let r=Math.sin(e),o=Math.cos(e),l=1-o,u=t[0],y=t[1],m=t[2],b=t[3],D=t[4],R=t[5],g=t[6],k=t[7],S=t[8],F=t[9],T=t[10],L=t[11],K=n*n*l+o,I=h*n*l+c*r,V=c*n*l-h*r,d=n*h*l-c*r,N=h*h*l+o,O=c*h*l+n*r,v=n*c*l+h*r,E=h*c*l-n*r,vt=c*c*l+o;return i[0]=u*K+D*I+S*V,i[1]=y*K+R*I+F*V,i[2]=m*K+g*I+T*V,i[3]=b*K+k*I+L*V,i[4]=u*d+D*N+S*O,i[5]=y*d+R*N+F*O,i[6]=m*d+g*N+T*O,i[7]=b*d+k*N+L*O,i[8]=u*v+D*E+S*vt,i[9]=y*v+R*E+F*vt,i[10]=m*v+g*E+T*vt,i[11]=b*v+k*E+L*vt,t!==i&&(i[12]=t[12],i[13]=t[13],i[14]=t[14],i[15]=t[15]),i}function Ye(i,t){let e=t[0],s=t[1],n=t[2];return i[12]+=i[0]*e+i[4]*s+i[7]*n,i[13]+=i[1]*e+i[5]*s+i[8]*n,i[14]+=i[2]*e+i[5]*s+i[9]*n,i[15]+=i[3]*e+i[6]*s+i[10]*n,i}const We=1,je=6,Qe=5,$e=8,ze=0,qe=4,Ge=7,Xe=3,Je=2;class Ze extends ae{_onPointerDown(t,e,s){const n=s.getShapes();function h(r,o,l){return{x:n[r].cart2d[o],y:n[r].cart2d[l]}}const c=h(1,2,3),a=h(0,0,1).y;e<a?s.x(t<c.x):e<.5?s.y(t<c.x):s.S(t>c.x)}_onPointerUp(t,e,s){const n=this.slope(this.xOnDown,this.yOnDown,t,e);switch(this.stickerOnDown){case We:s.F(t<this.xOnDown);break;case je:t<this.xOnDown?n<-.3?s.L(!1):s.U(!0):n<-.3?s.L(!0):s.U(!1);break;case Qe:s.U(t<this.xOnDown);break;case $e:t<this.xOnDown?n<.3?s.U(!0):s.R(!1):n<.3?s.U(!1):s.R(!0);break;case ze:s.Lw(t>this.xOnDown);break;case qe:s.L(t>this.xOnDown);break;case Ge:t<this.xOnDown?n<0?s.L(!1):s.R(!1):n<0?s.L(!0):s.R(!0);break;case Xe:s.R(t>this.xOnDown);break;case Je:s.Rw(t>this.xOnDown);break}}}const Q=1,$=6,xt=5,z=8,q=0,bt=4,G=7,Dt=3,X=2,J=11,Z=17,Rt=12,w=16,tt=10,St=14,et=15,gt=13,it=9,st=20,nt=25,kt=21,ht=26,ct=18,Lt=22,ot=24,At=23,rt=19,lt=28,ut=33,Ft=32,at=35,yt=27,Tt=31,mt=34,Ot=30,pt=29,Ut=Math.sqrt(6)/3,W=-1/3,ye=Math.sqrt(1-Ut*Ut-W*W),we=Math.sqrt(1-W*W),B=[Ut,W,ye],U=[0,W,-we],_=[-Ut,W,ye],C=[0,1,0],dt=[-B[0],-B[1],-B[2]],Nt=[-U[0],-U[1],-U[2]],Ht=[-_[0],-_[1],-_[2]],Yt=[-C[0],-C[1],-C[2]];class ti extends ue{constructor(t,e){super(e),this.stickers=[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35];let s=[..._,...C,...B],n=[...U,..._,...C],h=[...B,..._,...U],c=[...B,...C,...U];this.shapes=[...Pt(t,e,s,he),...Pt(t,e,h,ne),...Pt(t,e,n,oe),...Pt(t,e,c,ce)]}getHintType(t){return this.hintType||(this.hintType=H(t,[2,2,2,2])),this.hintType}getShapes(){return this.shapes}numStickers(){return 36}startDraggable(){return 0}endDraggable(){return 9}x(t){t?this.rotateAboutRightVertex(!1):this.rotateAboutLeftVertex(!0)}y(t){this.animate(t?Yt:C,()=>{this.cycle(t,Q,st,lt),this.cycle(t,$,nt,ut),this.cycle(t,xt,kt,Ft),this.cycle(t,z,ht,at),this.cycle(t,q,ct,yt),this.cycle(t,bt,Lt,Tt),this.cycle(t,G,ot,mt),this.cycle(t,Dt,At,Ot),this.cycle(t,X,rt,pt),this.cycle(t,J,tt,it),this.cycle(t,Z,w,et),this.cycle(t,Rt,gt,St)})}z(t){this.animate(t?U:Nt,()=>{this.cycle(t,J,ct,pt),this.cycle(t,Z,nt,mt),this.cycle(t,Rt,Lt,Ot),this.cycle(t,w,ot,at),this.cycle(t,lt,it,rt),this.cycle(t,Ft,gt,At),this.cycle(t,ut,et,ht),this.cycle(t,Tt,St,kt),this.cycle(t,yt,tt,st),this.cycle(t,Q,X,q),this.cycle(t,z,G,$),this.cycle(t,xt,Dt,bt)})}U(t){this.animate(t?Yt:C,()=>{this.cycle(t,Q,st,lt),this.cycle(t,$,nt,ut),this.cycle(t,xt,kt,Ft),this.cycle(t,z,ht,at)})}Uw(t){console.log("Uw not implemented for pyraminx.")}D(t){this.animate(t?Nt:U,()=>{this.cycle(t,J,pt,ct)})}Dw(t){console.log("Dw not implemented for pyraminx.")}F(t){this.animate(t?Yt:C,()=>{this.cycle(t,Q,st,lt)})}Fw(t){console.log("Fw not implemented for pyraminx.")}B(t){this.animate(t?Nt:U,()=>{this.cycle(t,J,pt,ct),this.cycle(t,Z,mt,nt),this.cycle(t,Rt,Ot,Lt),this.cycle(t,w,at,ot)})}Bw(t){console.log("Bw not implemented for pyraminx.")}L(t){this.animate(t?Ht:_,()=>{this.cycle(t,q,tt,rt),this.cycle(t,$,et,ot),this.cycle(t,bt,St,At),this.cycle(t,G,Z,ht)})}Lw(t){this.animate(t?Ht:_,()=>{this.cycle(t,q,tt,rt)})}R(t){this.animate(t?dt:B,()=>{this.cycle(t,X,yt,it),this.cycle(t,z,mt,et),this.cycle(t,Dt,Tt,gt),this.cycle(t,G,ut,w)})}Rw(t){this.animate(t?dt:B,()=>{this.cycle(t,X,yt,it)})}M(t){console.log("M not implemented for pyraminx.")}E(t){console.log("E not implemented for pyraminx.")}S(t){t?this.rotateAboutRightVertex(!0):this.rotateAboutLeftVertex(!1)}animate(t,e){this.affectedStickers=Array(36).fill(!1);const s=[...this.stickers];e(),this.animationQueue.push({axis:t,degrees:120,stickers:s,affectedStickers:[...this.affectedStickers]})}rotateAboutLeftVertex(t){this.animate(t?Ht:_,()=>{this.cycle(t,q,tt,rt),this.cycle(t,$,et,ot),this.cycle(t,bt,St,At),this.cycle(t,G,Z,ht),this.cycle(t,Q,it,ct),this.cycle(t,xt,gt,Lt),this.cycle(t,z,w,nt),this.cycle(t,Dt,Rt,kt),this.cycle(t,X,J,st),this.cycle(t,lt,yt,pt),this.cycle(t,at,ut,mt),this.cycle(t,Ft,Tt,Ot)})}rotateAboutRightVertex(t){this.animate(t?dt:B,()=>{this.cycle(t,X,yt,it),this.cycle(t,z,mt,et),this.cycle(t,Dt,Tt,gt),this.cycle(t,G,ut,w),this.cycle(t,Q,pt,tt),this.cycle(t,xt,Ot,St),this.cycle(t,$,at,Z),this.cycle(t,bt,Ft,Rt),this.cycle(t,q,lt,J),this.cycle(t,st,ct,rt),this.cycle(t,ht,nt,ot),this.cycle(t,kt,Lt,At)})}cycle(t,e,s,n){if(this.affectedStickers[e]=!0,this.affectedStickers[s]=!0,this.affectedStickers[n]=!0,t){const h=this.stickers[n];this.stickers[n]=this.stickers[s],this.stickers[s]=this.stickers[e],this.stickers[e]=h}else{const h=this.stickers[e];this.stickers[e]=this.stickers[s],this.stickers[s]=this.stickers[n],this.stickers[n]=h}}}function Pt(i,t,e,s){const n=$t(e,1.7),h=$t(e,1.75),c=zt($t(e,3.25),.5),a=Wt(n),r=Wt(h).map(u=>zt(u,.07)),o=Wt(c).map(u=>zt(u,.07));let l=new Array(9);for(let u=0;u<9;u++)l[u]=new Le(i,{perspective:t,color:s,baseVertices:a[u],stickerVertices:r[u],hintVertices:o[u]});return l}function Wt(i){const[t,e,s,n,h,c,a,r,o]=i,[l,u,y]=jt(t,e,s,n,h,c),[m,b,D]=Qt(t,e,s,n,h,c),[R,g,k]=jt(t,e,s,a,r,o),[S,F,T]=Qt(t,e,s,a,r,o),[L,K,I]=jt(n,h,c,a,r,o),[V,d,N]=Qt(n,h,c,a,r,o),O=(t+n+a)/3,v=(e+h+r)/3,E=(s+c+o)/3;return[[t,e,s,l,u,y,R,g,k],[m,b,D,n,h,c,L,K,I],[S,F,T,V,d,N,a,r,o],[O,v,E,S,F,T,V,d,N],[O,v,E,R,g,k,l,u,y],[O,v,E,m,b,D,L,K,I],[O,v,E,l,u,y,m,b,D],[O,v,E,R,g,k,S,F,T],[O,v,E,L,K,I,V,d,N]]}function jt(i,t,e,s,n,h){return[(2*i+s)/3,(2*t+n)/3,(2*e+h)/3]}function Qt(i,t,e,s,n,h){return[(i+2*s)/3,(t+2*n)/3,(e+2*h)/3]}function $t(i,t){return i.map(e=>e*t)}function zt(i,t){let e=1-t;const s=(i[0]+i[3]+i[6])/3,n=(i[1]+i[4]+i[7])/3,h=(i[2]+i[5]+i[8])/3,c=i[0]-s,a=i[1]-n,r=i[2]-h;i[0]=s+c*e,i[1]=n+a*e,i[2]=h+r*e;const o=i[3]-s,l=i[4]-n,u=i[5]-h;i[3]=s+o*e,i[4]=n+l*e,i[5]=h+u*e;const y=i[6]-s,m=i[7]-n,b=i[8]-h;return i[6]=s+y*e,i[7]=n+m*e,i[8]=h+b*e,i}function wt(){let i;return t=>(i||(i=t()),i)}const ei=100,ii=15;class me{constructor(){this.position=0,this.v=200,this.a=0,this.target=0}update(t){const e=-ei*(this.position-this.target),s=-ii*this.v;this.a=e+s,this.v+=this.a*t,this.position+=this.v*t}}let M=ni(),f=hi(M),Y=ci(f);const si=H(f,[0,0,0,0]);function ni(){const i=document.createElement("canvas");return i.style.position="fixed",i.style.display="block",i.style.top="0",i.style.left="0",i.style.width="100vw",i.style.height="100vh",i.style.zIndex="-1",document.body.appendChild(i),document.addEventListener("keydown",t=>{_t.forEach(e=>{e.enableKey(t)&&e.puzzle.matchKeyToTurn(t)})}),i}function hi(i){return i.getContext("webgl")}function ci(i){const t=`
    attribute vec4 aHintType;
    attribute vec4 aVertexPosition;
    attribute vec4 aVertexColor;
    uniform mat4 uTransformMatrix;
    uniform mat4 uRotateMatrix;

    varying lowp vec4 hintType;
    varying lowp vec4 vColor;
    varying lowp vec4 originalPos;
    varying lowp vec4 rotatedPos;
    void main(void) {
        gl_Position = uTransformMatrix * aVertexPosition;

        hintType = aHintType;
        originalPos = aVertexPosition;
        rotatedPos = uRotateMatrix * aVertexPosition;
        vColor = aVertexColor;
    }
    `,e=`
    varying lowp vec4 hintType;
    varying lowp vec4 vColor;
    varying lowp vec4 originalPos;
    varying lowp vec4 rotatedPos;

    void main(void) {
        gl_FragColor = vColor;

        // 0 means not a hint sticker
        if (hintType[0] <= 0.5) return;

        // 1 means it's a cube hint sticker
        else if (hintType[0] <= 1.5) {
            lowp float max = 1.05;

            // Don't discard if it is a normal sticker rather than a hint sticker.
            if (originalPos.x < max && originalPos.x > -max && originalPos.y < max && originalPos.y > -max && originalPos.z < max && originalPos.z > -max) {
                return;
            }

            // Don't discard if it starts on the right or left, and stays on its side.
            if (originalPos.x > max && rotatedPos.x > max) return;
            if (originalPos.x < -max && rotatedPos.x < -max) return;

            if (rotatedPos.y < max && rotatedPos.z < max) return;
        }

        // 2 means it's a pyraminx hint sticker
        else if (hintType[0] <= 2.5) {
            // Define a plane and keep the pixels behind the plane.
            lowp float plane = 0.55 * rotatedPos.y + 1.25 * rotatedPos.z;
            if (plane < 1.0) return;
        }

        discard;
    }
    `,s=se(i,i.VERTEX_SHADER,t),n=se(i,i.FRAGMENT_SHADER,e),h=i.createProgram();return i.attachShader(h,s),i.attachShader(h,n),i.linkProgram(h),i.getProgramParameter(h,i.LINK_STATUS)?(i.useProgram(h),{attributes:{hintType:i.getAttribLocation(h,"aHintType"),vertexPosition:i.getAttribLocation(h,"aVertexPosition"),vertexColor:i.getAttribLocation(h,"aVertexColor")},uniforms:{transformMatrix:i.getUniformLocation(h,"uTransformMatrix"),rotateMatrix:i.getUniformLocation(h,"uRotateMatrix")}}):(alert("Unable to initialize the shader program: "+i.getProgramInfoLog(h)),null)}let _t=[],Gt=[],te=Date.now()*.001,ee=!1;function pe(){ee||(ee=!0,requestAnimationFrame(be))}function oi(i,t=3){let e=fe(i),s=new Ie(f,e,t),n=new me,h=new Ne,c={puzzle:s,dragEnabled:!0,enableKey:r=>!0},a={div:i,puzzle:s,spring:n};return xe(i,h,c),_t.push(c),Gt.push(a),pe(),c}function ri(i){const t=fe(i);let e=new ti(f,t),s=new me,n=new Ze,h={puzzle:e,dragEnabled:!0,enableKey:a=>!0},c={div:i,puzzle:e,spring:s};return xe(i,n,h),_t.push(h),Gt.push(c),pe(),h}function fe(i){let t=Mt();return He(t,50*Math.PI/180,i.clientWidth/i.clientHeight,.1,100),Ye(t,[0,0,-5]),Bt(t,t,45*Math.PI/180,[1,0,0]),Bt(t,t,0,[0,-1,0]),t}function xe(i,t,e){const s=(o,l)=>{e.dragEnabled&&t.onPointerDown(o,l,i,e.puzzle)},n=(o,l)=>{e.dragEnabled&&t.onPointerMove(o,l)},h=()=>{e.dragEnabled&&t.onPointerUp(i,e.puzzle)},c=o=>{const l=o.target.getBoundingClientRect(),u=o.touches[0].pageX-l.left,y=o.touches[0].pageY-l.top;return{x:u,y}},a=()=>{i.addEventListener("pointerdown",o=>s(o.offsetX,o.offsetY)),i.addEventListener("pointermove",o=>n(o.offsetX,o.offsetY)),i.addEventListener("pointerup",()=>h())},r=()=>{i.addEventListener("touchstart",o=>{const{x:l,y:u}=c(o);s(l,u)}),i.addEventListener("touchmove",o=>{const{x:l,y:u}=c(o);n(l,u)}),i.addEventListener("touchend",()=>{h()})};window.PointerEvent?a():r(),i.style.touchAction="none"}function li(i,t){i.bindBuffer(i.ARRAY_BUFFER,t),i.vertexAttribPointer(Y.attributes.vertexPosition,3,i.FLOAT,!1,0,0),i.enableVertexAttribArray(Y.attributes.vertexPosition)}function ui(i,t){i.bindBuffer(i.ARRAY_BUFFER,t),i.vertexAttribPointer(Y.attributes.vertexColor,4,i.FLOAT,!1,0,0),i.enableVertexAttribArray(Y.attributes.vertexColor)}function ie(i,t){i.bindBuffer(i.ARRAY_BUFFER,t),i.vertexAttribPointer(Y.attributes.hintType,1,i.FLOAT,!1,0,0),i.enableVertexAttribArray(Y.attributes.hintType)}function se(i,t,e){const s=i.createShader(t);return i.shaderSource(s,e),i.compileShader(s),i.getShaderParameter(s,i.COMPILE_STATUS)?s:(alert("An error occurred compiling the shaders: "+i.getShaderInfoLog(s)),i.deleteShader(s),null)}function ai(){const i=M.clientWidth,t=M.clientHeight,e=M.width!==i||M.height!==t;return e&&(M.width=i,M.height=t),e}function qt(i,t,e,s){li(i,e),ui(i,s),t.drawElement(i)}function be(i){i*=.001;const t=i-te;te=i,ai(),f.enable(f.DEPTH_TEST),f.enable(f.SCISSOR_TEST),f.depthFunc(f.LEQUAL),f.clear(f.COLOR_BUFFER_BIT|f.DEPTH_BUFFER_BIT);for(let e=0;e<_t.length;e++){const{div:s,puzzle:n,spring:h}=Gt[e],c=s.getBoundingClientRect();if(c.bottom<0||c.top>M.clientHeight||c.right<0||c.left>M.clientWidth)continue;const a=c.right-c.left,r=c.bottom-c.top,o=c.left,l=M.clientHeight-c.bottom;if(f.viewport(o,l,a,r),f.scissor(o,l,a,r),n.animationQueue.length>0){const R=n.animationQueue[0];h.target=n.animationQueue.reduce((g,k)=>g+k.degrees,0),h.update(t),h.position>=R.degrees&&(n.affectedStickers=Array(n.numStickers()).fill(!1),h.position=0,n.animationQueue.shift())}const u=n.animationQueue[0];let y=n.getStickers(),m=wt(),b=wt();const D=n.getShapes();for(let R=0;R<D.length;R++){let g=y[R],k=D[g],S=D[R];const F=u&&u.affectedStickers[R]?m(()=>Bt(Mt(),n.perspective,h.position*Math.PI/180,u.axis)):n.perspective;f.uniformMatrix4fv(Y.uniforms.transformMatrix,!1,F);const T=u&&u.affectedStickers[R]?b(()=>{const L=Mt();return Bt(L,L,h.position*Math.PI/180,u.axis)}):Mt();f.uniformMatrix4fv(Y.uniforms.rotateMatrix,!1,T),ie(f,si),f.bindBuffer(f.ELEMENT_ARRAY_BUFFER,S.indexBuffer),qt(f,S,S.base,k.black),qt(f,S,S.sticker,k.color),ie(f,n.getHintType(f)),qt(f,S,S.hint,k.color)}}requestAnimationFrame(be)}const yi=document.querySelector("#scenesContainer");pi();for(let i=2;i<7;i++)mi(i);function De(){let i=document.createElement("div");return i.style.width="320px",i.style.height="320px",i.style.minWidth="320px",i.style.minHeight="320px",i.style.borderRadius="8px",i.style.boxShadow="rgb(255 255 255 / 20%) 0 4px 12px",yi.appendChild(i),i}function mi(i){let t=De();const e=oi(t,i);return e.enableKey=s=>!0,e}function pi(){let i=De();const t=ri(i);return t.enableKey=e=>!0,t}
