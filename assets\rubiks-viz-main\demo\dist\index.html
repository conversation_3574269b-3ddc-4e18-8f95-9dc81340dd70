<!DOCTYPE html>
<html lang="en" style="background-color: rgb(30, 30, 30); color: white;">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/rubiks-viz/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>rubiks viz demo</title>
    <script type="module" crossorigin src="/rubiks-viz/assets/index-f411fd23.js"></script>
  </head>
  <body>
    <h1>rubiks viz demo</h1>
    <p>Features and fun facts:</p>
    <ul>
      <li>rubiks viz is the library that powers <a href="https://cubingapp.com" style="color: lightblue;">CubingApp</a></li>
      <li>Can be used in any Javascript framework (React, Angular, Vue, Svelte, etc). You just need a reference to a div</li>
      <li>Uses WebGL for rendering</li>
      <li>Any size nxn and pyraminx</li>
      <li>Can render multiple cubes in a page</li>
      <li>Turn by clicking and dragging</li>
      <li>Turn with keyboard</li>
    </ul>
    <div
      id="scenesContainer"
      style="display: flex; flex-wrap: wrap; gap: 16px;"
    ></div>
    
  </body>
</html>
